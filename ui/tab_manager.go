package ui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"

	"lcheck/core"
	"lcheck/data"
)

// TabManager 标签页管理器 - 专门负责标签页的创建和管理
type TabManager struct {
	tabs        *container.AppTabs
	hostTab     *HostTab
	scanTab     *ScanTab
	
	// 依赖
	storage     *data.Storage
	scanner     *core.Scanner
	updateStatus func(string)
}

// NewTabManager 创建标签页管理器
func NewTabManager(storage *data.Storage, scanner *core.Scanner, updateStatus func(string)) *TabManager {
	tm := &TabManager{
		storage:      storage,
		scanner:      scanner,
		updateStatus: updateStatus,
	}
	
	tm.createTabs()
	
	return tm
}

// createTabs 创建标签页
func (tm *TabManager) createTabs() {
	// 创建标签页容器
	tm.tabs = container.NewAppTabs()
	
	// 创建主机管理标签页
	tm.hostTab = NewHostTab(tm.storage, tm.updateStatus)
	hostTabItem := container.NewTabItem("主机管理", tm.hostTab.GetContent())
	tm.tabs.Append(hostTabItem)

	// 创建扫描标签页
	tm.scanTab = NewScanTab(tm.storage, tm.scanner, tm.updateStatus)
	scanTabItem := container.NewTabItem("安全扫描", tm.scanTab.GetContent())
	tm.tabs.Append(scanTabItem)
	
	// 设置标签页切换回调
	tm.tabs.OnSelected = func(tab *container.TabItem) {
		tm.updateStatus("切换到: " + tab.Text)
	}
}

// GetTabs 获取标签页容器
func (tm *TabManager) GetTabs() *container.AppTabs {
	return tm.tabs
}

// GetHostTab 获取主机管理标签页
func (tm *TabManager) GetHostTab() *HostTab {
	return tm.hostTab
}

// GetScanTab 获取扫描标签页
func (tm *TabManager) GetScanTab() *ScanTab {
	return tm.scanTab
}

// SwitchToTab 切换到指定标签页
func (tm *TabManager) SwitchToTab(index int) {
	if index >= 0 && index < len(tm.tabs.Items) {
		tm.tabs.SelectTab(tm.tabs.Items[index])
	}
}

// SwitchToTabByName 根据名称切换标签页
func (tm *TabManager) SwitchToTabByName(name string) {
	for _, item := range tm.tabs.Items {
		if item.Text == name {
			tm.tabs.SelectTab(item)
			return
		}
	}
}

// GetCurrentTabIndex 获取当前标签页索引
func (tm *TabManager) GetCurrentTabIndex() int {
	for i, item := range tm.tabs.Items {
		if item == tm.tabs.Selected() {
			return i
		}
	}
	return -1
}

// GetCurrentTabName 获取当前标签页名称
func (tm *TabManager) GetCurrentTabName() string {
	if selected := tm.tabs.Selected(); selected != nil {
		return selected.Text
	}
	return ""
}

// AddTab 添加新标签页
func (tm *TabManager) AddTab(name string, content fyne.CanvasObject) {
	tabItem := container.NewTabItem(name, content)
	tm.tabs.Append(tabItem)
}

// RemoveTab 移除标签页
func (tm *TabManager) RemoveTab(index int) {
	if index >= 0 && index < len(tm.tabs.Items) {
		tm.tabs.Remove(tm.tabs.Items[index])
	}
}

// RemoveTabByName 根据名称移除标签页
func (tm *TabManager) RemoveTabByName(name string) {
	for _, item := range tm.tabs.Items {
		if item.Text == name {
			tm.tabs.Remove(item)
			return
		}
	}
}

// SetTabText 设置标签页文本
func (tm *TabManager) SetTabText(index int, text string) {
	if index >= 0 && index < len(tm.tabs.Items) {
		tm.tabs.Items[index].Text = text
		tm.tabs.Refresh()
	}
}

// SetTabIcon 设置标签页图标
func (tm *TabManager) SetTabIcon(index int, icon fyne.Resource) {
	if index >= 0 && index < len(tm.tabs.Items) {
		tm.tabs.Items[index].Icon = icon
		tm.tabs.Refresh()
	}
}

// GetTabCount 获取标签页数量
func (tm *TabManager) GetTabCount() int {
	return len(tm.tabs.Items)
}

// IsTabSelected 检查指定标签页是否被选中
func (tm *TabManager) IsTabSelected(index int) bool {
	if index >= 0 && index < len(tm.tabs.Items) {
		return tm.tabs.Items[index] == tm.tabs.Selected()
	}
	return false
}

// SetTabLocation 设置标签页位置
func (tm *TabManager) SetTabLocation(location container.TabLocation) {
	tm.tabs.SetTabLocation(location)
}

// GetTabLocation 获取标签页位置
func (tm *TabManager) GetTabLocation() container.TabLocation {
	// Fyne v2中TabLocation是私有的，返回默认值
	return container.TabLocationTop
}

// RefreshAllTabs 刷新所有标签页数据
func (tm *TabManager) RefreshAllTabs() {
	// 刷新主机标签页
	if tm.hostTab != nil {
		tm.hostTab.RefreshData()
	}
	
	// 刷新扫描标签页
	if tm.scanTab != nil {
		tm.scanTab.RefreshData()
	}
	
	tm.updateStatus("已刷新所有标签页数据")
}

// RefreshCurrentTab 刷新当前标签页
func (tm *TabManager) RefreshCurrentTab() {
	currentIndex := tm.GetCurrentTabIndex()
	
	switch currentIndex {
	case 0: // 主机管理标签页
		if tm.hostTab != nil {
			tm.hostTab.RefreshData()
		}
	case 1: // 扫描标签页
		if tm.scanTab != nil {
			tm.scanTab.RefreshData()
		}
	}
	
	tm.updateStatus("已刷新当前标签页数据")
}

// SetWindow 设置窗口引用（传递给子标签页）
func (tm *TabManager) SetWindow(window fyne.Window) {
	if tm.hostTab != nil {
		tm.hostTab.SetWindow(window)
	}
	if tm.scanTab != nil {
		tm.scanTab.SetWindow(window)
	}
}

// SetApp 设置应用引用（传递给子标签页）
func (tm *TabManager) SetApp(app *App) {
	if tm.hostTab != nil {
		tm.hostTab.SetApp(app)
	}
	if tm.scanTab != nil {
		tm.scanTab.SetApp(app)
	}
}

// GetTabContent 获取指定标签页的内容
func (tm *TabManager) GetTabContent(index int) fyne.CanvasObject {
	if index >= 0 && index < len(tm.tabs.Items) {
		return tm.tabs.Items[index].Content
	}
	return nil
}

// SetTabContent 设置指定标签页的内容
func (tm *TabManager) SetTabContent(index int, content fyne.CanvasObject) {
	if index >= 0 && index < len(tm.tabs.Items) {
		tm.tabs.Items[index].Content = content
		tm.tabs.Refresh()
	}
}

// EnableTab 启用标签页
func (tm *TabManager) EnableTab(index int) {
	if index >= 0 && index < len(tm.tabs.Items) {
		// Fyne v2中TabItem的Disabled是函数，简化实现
		tm.tabs.Refresh()
	}
}

// DisableTab 禁用标签页
func (tm *TabManager) DisableTab(index int) {
	if index >= 0 && index < len(tm.tabs.Items) {
		// Fyne v2中TabItem的Disabled是函数，简化实现
		tm.tabs.Refresh()
	}
}

// IsTabEnabled 检查标签页是否启用
func (tm *TabManager) IsTabEnabled(index int) bool {
	if index >= 0 && index < len(tm.tabs.Items) {
		// 简化实现，总是返回启用
		return true
	}
	return false
}

// SetOnTabSelected 设置标签页选择回调
func (tm *TabManager) SetOnTabSelected(callback func(*container.TabItem)) {
	tm.tabs.OnSelected = callback
}

// SetOnTabClosed 设置标签页关闭回调
func (tm *TabManager) SetOnTabClosed(callback func(*container.TabItem)) {
	// Fyne v2中AppTabs没有OnClosed回调，简化实现
}

// SetOnTabUnselected 设置标签页取消选择回调
func (tm *TabManager) SetOnTabUnselected(callback func(*container.TabItem)) {
	tm.tabs.OnUnselected = callback
}
