package ui

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
	"lcheck/services"
	"lcheck/ui/components"
)



// HostTab 主机管理选项卡 - 模块化门面，委托给专门的UI组件
type HostTab struct {
	// 服务层依赖
	hostService *services.HostService
	
	// 基础依赖
	updateStatus func(string)
	window       fyne.Window
	app          *App
	
	// UI组件 - 使用专门的组件，遵循模块化规范
	content      *fyne.Container
	toolbar      *components.HostToolbar
	hostTable    *components.HostTable
	groupList    *components.GroupList
	operations   *components.ItemOperations // 通用操作管理器
	
	// 数据
	hosts         []data.HostInfo
	groups        []data.HostGroup
	selectedHost  *data.HostInfo
	selectedGroup *data.HostGroup
}

// NewHostTab 创建主机管理选项卡 - 使用模块化组件
func NewHostTab(storage *data.Storage, updateStatus func(string)) *HostTab {
	// 初始化服务层
	hostService := services.NewHostService(storage, nil) // SSH客户端可以后续注入
	
	tab := &HostTab{
		hostService:  hostService,
		updateStatus: updateStatus,
		hosts:        make([]data.HostInfo, 0),
		groups:       make([]data.HostGroup, 0),
		operations:   components.NewItemOperations(nil), // 暂时传nil，后续设置window
	}
	
	tab.buildUI()
	tab.setupCallbacks()
	tab.loadData()
	
	return tab
}

// SetWindow 设置窗口引用
// 遵循模块化规范：延迟设置依赖
func (ht *HostTab) SetWindow(window fyne.Window) {
	ht.window = window
	ht.operations = components.NewItemOperations(window) // 重新初始化操作管理器
}

// SetApp 设置应用引用
func (ht *HostTab) SetApp(app *App) {
	ht.app = app
}

// buildUI 构建UI - 使用专门的组件，遵循模块化规范
func (ht *HostTab) buildUI() {
	// 创建专门的UI组件，遵循模块化规范：使用统一的组件
	ht.toolbar = components.NewHostToolbar()
	ht.hostTable = components.NewHostTable()
	ht.groupList = components.NewGroupList()
	
	// 设置统一的列表尺寸，遵循模块化规范：使用UI常量避免硬编码
	listSize := components.GetListSize()
	ht.hostTable.GetContainer().Resize(listSize)
	ht.groupList.SetSize(listSize)

	// 创建左右分割布局，遵循模块化规范：使用统一的组件接口
	leftPanel := container.NewBorder(
		widget.NewLabel("主机列表"), // 顶部标签
		nil,                        // 底部
		nil, nil,                   // 左右
		ht.hostTable.GetContainer(), // 中心：主机列表
	)

	rightPanel := container.NewBorder(
		widget.NewLabel("主机组"),   // 顶部标签
		nil,                        // 底部
		nil, nil,                   // 左右
		ht.groupList.GetContainer(), // 中心：主机组列表
	)

	split := container.NewHSplit(leftPanel, rightPanel)
	split.SetOffset(components.HSplitOffset) // 使用常量配置
	
	// 创建主布局
	ht.content = container.NewBorder(
		ht.toolbar.GetContainer(), // 顶部工具栏
		nil,                       // 底部为空
		nil, nil,                  // 左右为空
		split,                     // 中间分割面板
	)
}

// setupCallbacks 设置回调函数 - 连接组件和业务逻辑
func (ht *HostTab) setupCallbacks() {
	// 工具栏回调
	ht.toolbar.SetOnAddHost(ht.handleAddHost)
	ht.toolbar.SetOnAddGroup(ht.handleAddGroup)
	ht.toolbar.SetOnEditItem(ht.handleEditItem)
	ht.toolbar.SetOnDeleteItem(ht.handleDeleteItem)
	ht.toolbar.SetOnTestConnection(ht.handleTestConnection)
	ht.toolbar.SetOnImportData(ht.handleImportData)
	ht.toolbar.SetOnExportData(ht.handleExportData)
	ht.toolbar.SetOnRefreshData(ht.handleRefreshData)
	
	// 主机表格回调，遵循模块化规范：使用统一的事件处理接口
	ht.hostTable.SetOnSelection(ht.handleHostSelection)
	ht.hostTable.SetOnDoubleClick(ht.handleHostDoubleClick)

	// 主机组列表回调，遵循模块化规范：使用统一的事件处理接口
	ht.groupList.SetOnSelection(ht.handleGroupSelection)
	ht.groupList.SetOnDoubleClick(ht.handleGroupDoubleClick)
}

// GetContent 获取内容容器
func (ht *HostTab) GetContent() *fyne.Container {
	return ht.content
}

// ========== 数据操作方法 ==========

// loadData 加载数据 - 委托给服务层
func (ht *HostTab) loadData() {
	ht.loadHosts()
	ht.loadGroups()
}

// loadHosts 加载主机列表
func (ht *HostTab) loadHosts() {
	hosts, err := ht.hostService.LoadHosts()
	if err != nil {
		ht.showError("加载主机失败", err)
		return
	}
	
	ht.hosts = hosts
	ht.hostTable.SetHosts(hosts)
	ht.updateStatus(fmt.Sprintf("已加载 %d 个主机", len(hosts)))
}

// loadGroups 加载主机组列表
func (ht *HostTab) loadGroups() {
	groups, err := ht.hostService.LoadGroups()
	if err != nil {
		ht.showError("加载主机组失败", err)
		return
	}
	
	ht.groups = groups
	ht.groupList.SetGroups(groups) // 使用组件接口更新数据
	ht.updateStatus(fmt.Sprintf("已加载 %d 个主机组", len(groups)))
}

// refreshData 刷新数据
func (ht *HostTab) refreshData() {
	ht.loadData()
}

// RefreshData 刷新数据（公共方法）
func (ht *HostTab) RefreshData() {
	ht.refreshData()
}

// ========== 事件处理方法 ==========

// handleAddHost 处理添加主机
// 遵循模块化规范：使用可复用的操作组件
func (ht *HostTab) handleAddHost() {
	ht.operations.AddHost(ht.saveHost)
}

// handleAddGroup 处理添加主机组
// 遵循模块化规范：使用可复用的操作组件
func (ht *HostTab) handleAddGroup() {
	ht.operations.AddGroup(ht.saveGroup)
}

// handleEditItem 处理编辑项目
// 遵循模块化规范：使用可复用的操作组件
func (ht *HostTab) handleEditItem() {
	if ht.selectedHost != nil {
		ht.operations.EditHost(ht.selectedHost, ht.updateHost)
	} else if ht.selectedGroup != nil {
		ht.operations.EditGroup(ht.selectedGroup, ht.updateGroup)
	} else {
		ht.operations.ShowItemError("编辑失败", fmt.Errorf("请先选择要编辑的主机或主机组"))
		ht.updateStatus("请先选择要编辑的主机或主机组")
	}
}

// handleDeleteItem 处理删除项目
// 遵循模块化规范：使用可复用的操作组件
func (ht *HostTab) handleDeleteItem() {
	if ht.selectedHost != nil {
		ht.operations.DeleteHost(ht.selectedHost, ht.deleteSelectedHost)
	} else if ht.selectedGroup != nil {
		ht.operations.DeleteGroup(ht.selectedGroup, ht.deleteSelectedGroup)
	} else {
		ht.operations.ShowItemError("删除失败", fmt.Errorf("请先选择要删除的主机或主机组"))
		ht.updateStatus("请先选择要删除的主机或主机组")
	}
}

// handleTestConnection 处理测试连接
// 遵循模块化规范：使用可复用的操作组件，支持主机和主机组测试
func (ht *HostTab) handleTestConnection() {
	if ht.selectedHost != nil {
		// 单个主机测试，显示简单对话框
		ht.operations.TestHostConnection(ht.selectedHost, ht.testConnection)
	} else if ht.selectedGroup != nil {
		// 主机组测试，显示详细结果对话框
		ht.operations.TestGroupConnections(ht.selectedGroup, ht.testConnectionForGroup)
	} else {
		ht.operations.ShowItemError("测试失败", fmt.Errorf("请先选择要测试的主机或主机组"))
		ht.updateStatus("请先选择要测试的主机或主机组")
	}
}

// handleImportData 处理导入数据
func (ht *HostTab) handleImportData() {
	ht.showImportDialog()
}

// handleExportData 处理导出数据
func (ht *HostTab) handleExportData() {
	ht.showExportDialog()
}

// handleRefreshData 处理刷新数据
func (ht *HostTab) handleRefreshData() {
	ht.refreshData()
}

// handleHostSelection 处理主机选择
// 遵循模块化规范：统一的状态管理，确保选择状态互斥
func (ht *HostTab) handleHostSelection(host *data.HostInfo) {
	// 如果选择了主机，清除主机组选择状态
	if host != nil {
		ht.selectedGroup = nil
		ht.groupList.ClearSelection()
	}

	ht.selectedHost = host

	// 更新工具栏状态
	ht.toolbar.SetSelectedItems(host, nil)

	if host != nil {
		ht.updateStatus(fmt.Sprintf("已选择主机: %s (%s:%s)", host.Name, host.Host, host.Port))
	} else {
		ht.updateStatus("未选择主机")
	}
}

// handleHostDoubleClick 处理主机双击
func (ht *HostTab) handleHostDoubleClick(host *data.HostInfo) {
	if host != nil {
		ht.showHostDetails(host)
	}
}

// handleGroupSelection 处理主机组选择
// 遵循模块化规范：统一的状态管理，确保选择状态互斥
func (ht *HostTab) handleGroupSelection(group *data.HostGroup) {
	// 如果选择了主机组，清除主机选择状态
	if group != nil {
		ht.selectedHost = nil
		ht.hostTable.ClearSelection()
	}

	ht.selectedGroup = group

	// 更新工具栏状态
	ht.toolbar.SetSelectedItems(nil, group)

	if group != nil {
		ht.updateStatus(fmt.Sprintf("已选择主机组: %s (%d台主机)", group.Name, len(group.Hosts)))
	} else {
		ht.updateStatus("未选择主机组")
	}
}

// handleGroupDoubleClick 处理主机组双击
// 遵循模块化规范：统一的事件处理模式
func (ht *HostTab) handleGroupDoubleClick(group *data.HostGroup) {
	if group != nil {
		ht.showGroupDetails(group)
	}
}

// ========== 业务逻辑方法 ==========

// testConnection 测试连接
// 遵循模块化规范：委托给服务层处理业务逻辑
func (ht *HostTab) testConnection(host *data.HostInfo) {
	ht.updateStatus(fmt.Sprintf("正在测试连接到 %s (%s:%s)...", host.Name, host.Host, host.Port))

	// 使用defer来捕获可能的panic
	defer func() {
		if r := recover(); r != nil {
			ht.updateStatus(fmt.Sprintf("连接测试失败: %v", r))
			components.ShowErrorDialog("连接测试失败", fmt.Errorf("%v", r), ht.window)
		}
	}()

	// 委托给服务层测试连接
	result := ht.hostService.TestConnection(*host)

	// 根据测试结果显示相应的消息
	if result.Status == "连接成功" {
		successMsg := fmt.Sprintf("主机 %s 连接测试成功\n耗时: %v", host.Name, result.Duration)
		ht.updateStatus(successMsg)
		components.ShowSuccessDialog("连接测试成功", successMsg, ht.window)
	} else {
		errorMsg := fmt.Sprintf("主机 %s 连接测试失败\n状态: %s\n错误: %s\n耗时: %v",
			host.Name, result.Status, result.Error, result.Duration)
		ht.updateStatus(fmt.Sprintf("主机 %s 连接测试失败: %s", host.Name, result.Status))
		components.ShowErrorDialog("连接测试失败", fmt.Errorf(errorMsg), ht.window)
	}
}

// testConnectionForGroup 为主机组测试连接（返回结果而不显示对话框）
// 遵循模块化规范：专门为批量测试设计的方法
func (ht *HostTab) testConnectionForGroup(host *data.HostInfo) *components.ConnectionTestResult {
	// 委托给服务层测试连接
	result := ht.hostService.TestConnection(*host)

	// 转换为组件需要的结果格式
	return &components.ConnectionTestResult{
		Status:   result.Status,
		Error:    result.Error,
		Duration: result.Duration,
	}
}

// ========== 对话框方法 ==========

// showAddHostDialog 显示添加主机对话框
func (ht *HostTab) showAddHostDialog() {
	ht.updateStatus("正在打开添加主机对话框...")

	components.CreateHostFormDialog("添加主机", nil, func(name, hostAddr, username, password string, port int) {
		// 创建主机
		host, err := ht.hostService.CreateHost(name, hostAddr, username, password, port)
		if err != nil {
			ht.updateStatus(fmt.Sprintf("创建主机失败: %v", err))
			components.ShowErrorDialog("创建失败", err, ht.window)
			return
		}

		// 刷新数据
		ht.refreshData()
		ht.updateStatus(fmt.Sprintf("主机 %s 已添加", host.Name))
		components.ShowSuccessDialog("添加成功", fmt.Sprintf("主机 %s 已成功添加", host.Name), ht.window)
	}, ht.window)
}

// showComplexAddHostDialog 显示复杂的添加主机对话框
func (ht *HostTab) showComplexAddHostDialog(nameEntry, hostEntry, portEntry, usernameEntry *widget.Entry, passwordEntry *widget.Entry, descEntry *widget.Entry) {
	ht.updateStatus("创建复杂对话框...")

	// 创建表单
	form := container.NewVBox(
		widget.NewLabel("添加新主机"),
		widget.NewSeparator(),
		widget.NewForm(
			widget.NewFormItem("主机名称", nameEntry),
			widget.NewFormItem("主机地址", hostEntry),
			widget.NewFormItem("端口", portEntry),
			widget.NewFormItem("用户名", usernameEntry),
			widget.NewFormItem("密码", passwordEntry),
			widget.NewFormItem("描述", descEntry),
		),
	)

	// 创建对话框
	dialog := dialog.NewCustomConfirm("添加主机", "添加", "取消", form, func(confirmed bool) {
		if !confirmed {
			return
		}

		// 验证输入
		if nameEntry.Text == "" || hostEntry.Text == "" || usernameEntry.Text == "" {
			ht.showError("输入错误", fmt.Errorf("主机名称、地址和用户名不能为空"))
			return
		}

		// 解析端口
		port := 22
		if portEntry.Text != "" {
			if p, err := fmt.Sscanf(portEntry.Text, "%d", &port); err != nil || p != 1 {
				ht.showError("输入错误", fmt.Errorf("端口必须是数字"))
				return
			}
		}

		// 创建主机
		host, err := ht.hostService.CreateHost(
			nameEntry.Text,
			hostEntry.Text,
			usernameEntry.Text,
			passwordEntry.Text,
			port,
		)
		if err != nil {
			ht.showError("创建主机失败", err)
			return
		}

		// 刷新数据
		ht.refreshData()
		ht.updateStatus(fmt.Sprintf("主机 %s 已添加", host.Name))
	}, ht.window)

	dialog.Resize(fyne.NewSize(400, 500))
	dialog.Show()
}

// showAddGroupDialog 显示添加主机组对话框
func (ht *HostTab) showAddGroupDialog() {
	ht.updateStatus("正在打开添加主机组对话框...")

	components.ShowAddGroupWithHostsDialog(func(groupName, groupDesc string, hosts []components.GroupHostInfo) {
		// 验证并创建组内主机
		var createdHostIDs []string
		for _, hostInfo := range hosts {
			// 创建主机
			host, err := ht.hostService.CreateHost(
				hostInfo.NameEntry.Text,
				hostInfo.AddrEntry.Text,
				hostInfo.UserEntry.Text,
				hostInfo.PassEntry.Text,
				22, // 默认端口
			)
			if err != nil {
				ht.updateStatus(fmt.Sprintf("创建主机 %s 失败: %v", hostInfo.NameEntry.Text, err))
				components.ShowErrorDialog("创建主机失败", err, ht.window)
				return
			}
			createdHostIDs = append(createdHostIDs, host.ID)
		}

		// 创建主机组
		group, err := ht.hostService.CreateGroup(groupName, groupDesc, createdHostIDs)
		if err != nil {
			ht.updateStatus(fmt.Sprintf("创建主机组失败: %v", err))
			components.ShowErrorDialog("创建主机组失败", err, ht.window)
			return
		}

		// 刷新数据
		ht.refreshData()
		ht.updateStatus(fmt.Sprintf("主机组 %s 已创建，包含 %d 个新主机", group.Name, len(createdHostIDs)))
		components.ShowSuccessDialog("创建成功",
			fmt.Sprintf("主机组 %s 已成功创建，包含 %d 个主机", group.Name, len(createdHostIDs)),
			ht.window)
	}, ht.window)
}

// showEditHostDialog 显示编辑主机对话框
func (ht *HostTab) showEditHostDialog(host *data.HostInfo) {
	ht.updateStatus(fmt.Sprintf("正在打开编辑主机 %s 对话框...", host.Name))

	components.CreateHostFormDialog("编辑主机", host, func(name, hostAddr, username, password string, port int) {
		// 更新主机
		err := ht.hostService.UpdateHost(host.ID, name, hostAddr, username, password, port)
		if err != nil {
			ht.updateStatus(fmt.Sprintf("更新主机失败: %v", err))
			components.ShowErrorDialog("更新失败", err, ht.window)
			return
		}

		// 刷新数据
		ht.refreshData()
		ht.updateStatus(fmt.Sprintf("主机 %s 已更新", host.Name))
		components.ShowSuccessDialog("更新成功", fmt.Sprintf("主机 %s 已成功更新", host.Name), ht.window)
	}, ht.window)
}

// showEditGroupDialog 显示编辑主机组对话框
func (ht *HostTab) showEditGroupDialog(group *data.HostGroup) {
	ht.updateStatus(fmt.Sprintf("点击了编辑主机组 %s 按钮 - 功能正在开发中", group.Name))
}

// showDeleteHostConfirmDialog 显示删除主机确认对话框
func (ht *HostTab) showDeleteHostConfirmDialog() {
	if ht.selectedHost == nil {
		ht.updateStatus("错误: 没有选择要删除的主机")
		return
	}

	ht.updateStatus(fmt.Sprintf("确认删除主机 %s", ht.selectedHost.Name))

	message := fmt.Sprintf("确定要删除主机 '%s' 吗？\n\n主机地址: %s:%s\n用户名: %s\n\n此操作不可撤销！",
		ht.selectedHost.Name, ht.selectedHost.Host, ht.selectedHost.Port, ht.selectedHost.Username)

	components.ShowConfirmDialog("确认删除", message, func() {
		ht.deleteHost(ht.selectedHost)
	}, ht.window)
}

// showDeleteGroupConfirmDialog 显示删除主机组确认对话框
func (ht *HostTab) showDeleteGroupConfirmDialog() {
	ht.updateStatus(fmt.Sprintf("点击了删除主机组 %s 按钮 - 功能正在开发中", ht.selectedGroup.Name))
}

// showHostDetails 显示主机详情
func (ht *HostTab) showHostDetails(host *data.HostInfo) {
	details := fmt.Sprintf("主机详情 - 名称: %s, 地址: %s:%s, 用户名: %s",
		host.Name, host.Host, host.Port, host.Username)
	ht.updateStatus(details)
}

// showGroupDetails 显示主机组详情
// 遵循模块化规范：统一的详情显示模式
func (ht *HostTab) showGroupDetails(group *data.HostGroup) {
	details := fmt.Sprintf("主机组详情 - 名称: %s, 描述: %s, 主机数量: %d",
		group.Name, group.Description, len(group.Hosts))
	ht.updateStatus(details)
}

// showImportDialog 显示导入对话框
// 遵循模块化规范：使用统一的对话框组件
func (ht *HostTab) showImportDialog() {
	ht.updateStatus("正在打开导入对话框...")

	// 创建文件选择对话框
	fileDialog := dialog.NewFileOpen(func(reader fyne.URIReadCloser, err error) {
		if err != nil {
			components.ShowErrorDialog("文件选择失败", err, ht.window)
			return
		}
		if reader == nil {
			ht.updateStatus("取消导入")
			return
		}
		defer reader.Close()

		// 委托给服务层处理导入
		ht.importHostsFromFile(reader)
	}, ht.window)

	// 设置文件过滤器，只显示JSON文件
	fileDialog.Show()
}

// importHostsFromFile 从文件导入主机数据
// 遵循模块化规范：委托给服务层处理业务逻辑
func (ht *HostTab) importHostsFromFile(reader fyne.URIReadCloser) {
	ht.updateStatus("正在读取导入文件...")

	// 这里可以添加实际的导入逻辑
	// 目前显示一个提示对话框
	components.ShowSuccessDialog("导入功能",
		"导入功能正在开发中\n\n支持的格式：\n- JSON格式的主机配置文件\n- 包含主机名称、地址、端口、用户名等信息",
		ht.window)

	ht.updateStatus("导入功能开发中...")
}

// showExportDialog 显示导出对话框
// 遵循模块化规范：使用统一的对话框组件
func (ht *HostTab) showExportDialog() {
	ht.updateStatus("正在打开导出对话框...")

	if len(ht.hosts) == 0 {
		components.ShowErrorDialog("导出失败", fmt.Errorf("没有主机数据可导出"), ht.window)
		ht.updateStatus("没有主机数据可导出")
		return
	}

	// 创建文件保存对话框
	fileDialog := dialog.NewFileSave(func(writer fyne.URIWriteCloser, err error) {
		if err != nil {
			components.ShowErrorDialog("文件保存失败", err, ht.window)
			return
		}
		if writer == nil {
			ht.updateStatus("取消导出")
			return
		}
		defer writer.Close()

		// 委托给服务层处理导出
		ht.exportHostsToFile(writer)
	}, ht.window)

	// 设置默认文件名
	fileDialog.SetFileName("hosts_export.json")
	fileDialog.Show()
}

// exportHostsToFile 导出主机数据到文件
// 遵循模块化规范：委托给服务层处理业务逻辑
func (ht *HostTab) exportHostsToFile(writer fyne.URIWriteCloser) {
	ht.updateStatus("正在导出主机数据...")

	// 这里可以添加实际的导出逻辑
	// 目前显示一个成功提示
	successMsg := fmt.Sprintf("导出功能开发中\n\n将要导出 %d 个主机的配置信息\n包括主机名称、地址、端口、用户名等", len(ht.hosts))
	components.ShowSuccessDialog("导出功能", successMsg, ht.window)

	ht.updateStatus(fmt.Sprintf("导出功能开发中 - 共 %d 个主机", len(ht.hosts)))
}

// deleteHost 删除主机
func (ht *HostTab) deleteHost(host *data.HostInfo) {
	err := ht.hostService.DeleteHost(host.ID)
	if err != nil {
		ht.showError("删除主机失败", err)
		return
	}
	
	ht.refreshData()
	ht.selectedHost = nil
	ht.toolbar.SetSelectedItems(nil, nil)
	ht.updateStatus("主机已删除")
}

// deleteGroup 删除主机组
func (ht *HostTab) deleteGroup(group *data.HostGroup) {
	err := ht.hostService.DeleteGroup(group.ID)
	if err != nil {
		ht.showError("删除主机组失败", err)
		return
	}
	
	ht.refreshData()
	ht.selectedGroup = nil
	ht.toolbar.SetSelectedItems(nil, nil)
	ht.updateStatus("主机组已删除")
}

// ========== 辅助方法 ==========

// showError 显示错误对话框
func (ht *HostTab) showError(title string, err error) {
	ht.updateStatus(fmt.Sprintf("错误 - %s: %s", title, err.Error()))
	components.ShowErrorDialog(title, err, ht.window)
}

// showInfo 显示信息对话框
func (ht *HostTab) showInfo(title, message string) {
	ht.updateStatus(fmt.Sprintf("%s: %s", title, message))
	components.ShowSuccessDialog(title, message, ht.window)
}

// ========== 模块化回调方法 ==========

// saveHost 保存主机 - 模块化回调函数
func (ht *HostTab) saveHost(name, hostAddr, username, password string, port int) {
	ht.updateStatus(fmt.Sprintf("正在保存主机: %s (%s:%d)...", name, hostAddr, port))

	// 调用服务层保存主机
	host, err := ht.hostService.CreateHost(name, hostAddr, username, password, port)
	if err != nil {
		components.ShowErrorDialog("保存失败", err, ht.window)
		ht.updateStatus(fmt.Sprintf("保存主机失败: %v", err))
		return
	}

	// 添加到本地列表并更新UI
	ht.hosts = append(ht.hosts, *host)
	ht.hostTable.SetHosts(ht.hosts)

	// 显示成功消息
	components.ShowSuccessDialog("保存成功", fmt.Sprintf("主机 '%s' 已成功添加", name), ht.window)
	ht.updateStatus(fmt.Sprintf("主机 '%s' 保存成功", name))
}

// updateHost 更新主机 - 模块化回调函数
func (ht *HostTab) updateHost(name, hostAddr, username, password string, port int) {
	if ht.selectedHost == nil {
		components.ShowErrorDialog("更新失败", fmt.Errorf("没有选择要更新的主机"), ht.window)
		return
	}

	ht.updateStatus(fmt.Sprintf("正在更新主机: %s (%s:%d)...", name, hostAddr, port))

	// 调用服务层更新主机
	err := ht.hostService.UpdateHost(ht.selectedHost.ID, name, hostAddr, username, password, port)
	if err != nil {
		components.ShowErrorDialog("更新失败", err, ht.window)
		ht.updateStatus(fmt.Sprintf("更新主机失败: %v", err))
		return
	}

	// 更新本地列表中的主机信息
	for i, host := range ht.hosts {
		if host.ID == ht.selectedHost.ID {
			ht.hosts[i].Name = name
			ht.hosts[i].Host = hostAddr
			ht.hosts[i].Username = username
			ht.hosts[i].Password = password
			ht.hosts[i].Port = fmt.Sprintf("%d", port)
			break
		}
	}

	// 更新UI
	ht.hostTable.SetHosts(ht.hosts)

	// 显示成功消息
	components.ShowSuccessDialog("更新成功", fmt.Sprintf("主机 '%s' 已成功更新", name), ht.window)
	ht.updateStatus(fmt.Sprintf("主机 '%s' 更新成功", name))
}

// deleteSelectedHost 删除选中的主机 - 模块化回调函数
func (ht *HostTab) deleteSelectedHost() {
	if ht.selectedHost != nil {
		ht.deleteHost(ht.selectedHost)
	}
}

// saveGroup 保存主机组 - 模块化回调函数
func (ht *HostTab) saveGroup(name, description string, hosts []components.GroupHostInfo) {
	ht.updateStatus(fmt.Sprintf("正在保存主机组: %s...", name))

	// 转换GroupHostInfo为HostInfo
	var groupHosts []data.HostInfo
	for i, hostInfo := range hosts {
		host := data.HostInfo{
			ID:       fmt.Sprintf("host_%s_%d", name, i+1), // 生成主机ID
			Name:     hostInfo.NameEntry.Text,
			Host:     hostInfo.AddrEntry.Text,
			Username: hostInfo.UserEntry.Text,
			Password: hostInfo.PassEntry.Text,
			Port:     "22", // 默认端口
		}
		groupHosts = append(groupHosts, host)
	}

	// 创建新的主机组
	newGroup := data.HostGroup{
		ID:          fmt.Sprintf("group_%d", len(ht.groups)+1), // 简单的ID生成
		Name:        name,
		Description: description,
		Hosts:       groupHosts, // 包含组内主机
	}

	// 添加到本地列表
	ht.groups = append(ht.groups, newGroup)

	// 调用服务层持久化保存
	err := ht.hostService.SaveGroups(ht.groups)
	if err != nil {
		// 保存失败，从本地列表中移除
		ht.groups = ht.groups[:len(ht.groups)-1]
		components.ShowErrorDialog("保存失败", err, ht.window)
		ht.updateStatus(fmt.Sprintf("保存主机组失败: %v", err))
		return
	}

	// 更新UI
	ht.groupList.SetGroups(ht.groups)

	// 显示成功消息
	hostCount := len(groupHosts)
	components.ShowSuccessDialog("保存成功",
		fmt.Sprintf("主机组 '%s' 已成功添加\n包含 %d 台主机", name, hostCount), ht.window)
	ht.updateStatus(fmt.Sprintf("主机组 '%s' 保存成功，包含 %d 台主机", name, hostCount))
}

// updateGroup 更新主机组 - 模块化回调函数
func (ht *HostTab) updateGroup(name, description string, hosts []components.GroupHostInfo) {
	if ht.selectedGroup == nil {
		components.ShowErrorDialog("更新失败", fmt.Errorf("没有选择要更新的主机组"), ht.window)
		return
	}

	ht.updateStatus(fmt.Sprintf("正在更新主机组: %s...", name))

	// 转换GroupHostInfo为HostInfo
	var groupHosts []data.HostInfo
	for i, hostInfo := range hosts {
		host := data.HostInfo{
			ID:       fmt.Sprintf("host_%s_%d", name, i+1), // 生成主机ID
			Name:     hostInfo.NameEntry.Text,
			Host:     hostInfo.AddrEntry.Text,
			Username: hostInfo.UserEntry.Text,
			Password: hostInfo.PassEntry.Text,
			Port:     "22", // 默认端口
		}
		groupHosts = append(groupHosts, host)
	}

	// 保存原始数据，以便回滚
	var originalGroup *data.HostGroup
	var groupIndex int = -1

	// 更新本地列表中的主机组信息
	for i, group := range ht.groups {
		if group.ID == ht.selectedGroup.ID {
			originalGroup = &group // 保存原始数据
			groupIndex = i
			ht.groups[i].Name = name
			ht.groups[i].Description = description
			ht.groups[i].Hosts = groupHosts // 更新主机列表
			break
		}
	}

	// 调用服务层持久化保存
	err := ht.hostService.SaveGroups(ht.groups)
	if err != nil {
		// 保存失败，回滚到原始数据
		if originalGroup != nil && groupIndex >= 0 {
			ht.groups[groupIndex] = *originalGroup
		}
		components.ShowErrorDialog("更新失败", err, ht.window)
		ht.updateStatus(fmt.Sprintf("更新主机组失败: %v", err))
		return
	}

	// 更新UI
	ht.groupList.SetGroups(ht.groups)

	// 显示成功消息
	hostCount := len(groupHosts)
	components.ShowSuccessDialog("更新成功",
		fmt.Sprintf("主机组 '%s' 已成功更新\n包含 %d 台主机", name, hostCount), ht.window)
	ht.updateStatus(fmt.Sprintf("主机组 '%s' 更新成功，包含 %d 台主机", name, hostCount))
}

// deleteSelectedGroup 删除选中的主机组 - 模块化回调函数
func (ht *HostTab) deleteSelectedGroup() {
	if ht.selectedGroup != nil {
		ht.deleteGroup(ht.selectedGroup)
	}
}
