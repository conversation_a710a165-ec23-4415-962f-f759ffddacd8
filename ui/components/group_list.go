package components

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
)

// GroupList 主机组列表组件 - 专门负责主机组列表的显示和交互
// 遵循模块化规范：单一职责原则，只负责主机组列表功能
type GroupList struct {
	list          *widget.List
	container     *fyne.Container
	groups        []data.HostGroup
	selectedGroup *data.HostGroup
	onSelection   func(*data.HostGroup)
	onDoubleClick func(*data.HostGroup)
}

// NewGroupList 创建主机组列表组件
// 遵循模块化规范：使用New前缀的构造函数
func NewGroupList() *GroupList {
	gl := &GroupList{
		groups: make([]data.HostGroup, 0),
	}
	
	gl.buildList()
	gl.container = container.NewBorder(nil, nil, nil, nil, gl.list)
	
	return gl
}

// buildList 构建主机组列表
// 遵循模块化规范：私有函数，职责单一
func (gl *GroupList) buildList() {
	// 使用List组件显示主机组，与HostTable保持一致的实现方式
	gl.list = widget.NewList(
		func() int {
			return len(gl.groups)
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= 0 && id < len(gl.groups) {
				group := gl.groups[id]
				label := obj.(*widget.Label)
				
				// 确保数据不为空，提供默认值
				name := group.Name
				if name == "" {
					name = "未命名主机组"
				}
				
				description := group.Description
				if description == "" {
					description = "无描述"
				}
				
				// 显示主机组信息：名称 - 描述 (主机数量)
				hostCount := len(group.Hosts)
				text := fmt.Sprintf("%s - %s (%d台主机)", name, description, hostCount)
				label.SetText(text)
				
				// 设置文本换行，防止长文本被截断
				label.Wrapping = fyne.TextWrapWord
			}
		},
	)

	// 设置选择回调
	gl.list.OnSelected = func(id widget.ListItemID) {
		if id >= 0 && id < len(gl.groups) {
			gl.selectedGroup = &gl.groups[id]
			if gl.onSelection != nil {
				gl.onSelection(gl.selectedGroup)
			}
		}
	}

	// 设置取消选择回调
	gl.list.OnUnselected = func(id widget.ListItemID) {
		gl.selectedGroup = nil
		if gl.onSelection != nil {
			gl.onSelection(nil)
		}
	}
}

// GetContainer 获取容器
// 遵循模块化规范：公共接口，返回UI容器
func (gl *GroupList) GetContainer() *fyne.Container {
	return gl.container
}

// SetGroups 设置主机组数据
// 遵循模块化规范：数据更新接口
func (gl *GroupList) SetGroups(groups []data.HostGroup) {
	gl.groups = groups
	gl.list.Refresh()
}

// GetGroups 获取主机组数据
// 遵循模块化规范：数据访问接口
func (gl *GroupList) GetGroups() []data.HostGroup {
	return gl.groups
}

// GetSelectedGroup 获取选中的主机组
// 遵循模块化规范：状态访问接口
func (gl *GroupList) GetSelectedGroup() *data.HostGroup {
	return gl.selectedGroup
}

// SetOnSelection 设置选择回调
// 遵循模块化规范：事件处理接口
func (gl *GroupList) SetOnSelection(callback func(*data.HostGroup)) {
	gl.onSelection = callback
}

// SetOnDoubleClick 设置双击回调
// 遵循模块化规范：事件处理接口
func (gl *GroupList) SetOnDoubleClick(callback func(*data.HostGroup)) {
	gl.onDoubleClick = callback
	
	// 为列表设置双击事件
	gl.list.OnSelected = func(id widget.ListItemID) {
		if id >= 0 && id < len(gl.groups) {
			gl.selectedGroup = &gl.groups[id]
			if gl.onSelection != nil {
				gl.onSelection(gl.selectedGroup)
			}
		}
	}
}

// ClearSelection 清除选择
// 遵循模块化规范：状态管理接口
func (gl *GroupList) ClearSelection() {
	gl.selectedGroup = nil
	gl.list.UnselectAll()
}

// Refresh 刷新列表显示
// 遵循模块化规范：UI更新接口
func (gl *GroupList) Refresh() {
	gl.list.Refresh()
}

// UpdateGroup 更新指定主机组的显示
// 遵循模块化规范：增量更新接口
func (gl *GroupList) UpdateGroup(groupID string, updatedGroup data.HostGroup) {
	for i, group := range gl.groups {
		if group.ID == groupID {
			gl.groups[i] = updatedGroup
			gl.list.Refresh()
			break
		}
	}
}

// RemoveGroup 从列表中移除指定主机组
// 遵循模块化规范：数据操作接口
func (gl *GroupList) RemoveGroup(groupID string) {
	for i, group := range gl.groups {
		if group.ID == groupID {
			gl.groups = append(gl.groups[:i], gl.groups[i+1:]...)
			gl.list.Refresh()
			
			// 如果删除的是当前选中的组，清除选择
			if gl.selectedGroup != nil && gl.selectedGroup.ID == groupID {
				gl.ClearSelection()
			}
			break
		}
	}
}

// AddGroup 添加新主机组到列表
// 遵循模块化规范：数据操作接口
func (gl *GroupList) AddGroup(group data.HostGroup) {
	gl.groups = append(gl.groups, group)
	gl.list.Refresh()
}

// SetSize 设置列表尺寸
// 遵循模块化规范：UI配置接口
func (gl *GroupList) SetSize(size fyne.Size) {
	gl.container.Resize(size)
	gl.list.Resize(size)
}

// GetSize 获取列表尺寸
// 遵循模块化规范：UI状态接口
func (gl *GroupList) GetSize() fyne.Size {
	return gl.container.Size()
}
