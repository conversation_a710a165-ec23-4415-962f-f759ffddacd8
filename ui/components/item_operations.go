package components

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"

	"lcheck/data"
)

// ItemOperations 通用项目操作管理器
// 遵循模块化规范：可复用的操作组件，支持主机和主机组的统一操作
type ItemOperations struct {
	window fyne.Window
}

// NewItemOperations 创建项目操作管理器
// 遵循模块化规范：使用New前缀的构造函数
func NewItemOperations(window fyne.Window) *ItemOperations {
	return &ItemOperations{
		window: window,
	}
}

// EditHost 编辑主机
// 遵循模块化规范：可复用的主机编辑功能
func (io *ItemOperations) EditHost(host *data.HostInfo, onSave func(name, hostAddr, username, password string, port int)) {
	if host == nil {
		ShowErrorDialog("编辑失败", fmt.Errorf("主机信息为空"), io.window)
		return
	}

	// 使用统一的主机表单对话框
	CreateHostFormDialog("编辑主机", host, onSave, io.window)
}

// EditGroup 编辑主机组
// 遵循模块化规范：可复用的主机组编辑功能，使用和添加一样的完整对话框
func (io *ItemOperations) EditGroup(group *data.HostGroup, onSave func(name, description string, hosts []GroupHostInfo)) {
	if group == nil {
		ShowErrorDialog("编辑失败", fmt.Errorf("主机组信息为空"), io.window)
		return
	}

	// 使用完整的编辑对话框，和添加主机组保持一致的用户体验
	ShowEditGroupWithHostsDialog(group, func(groupName, groupDesc string, hosts []GroupHostInfo) {
		// 传递完整的信息，包括组内主机
		onSave(groupName, groupDesc, hosts)
	}, io.window)
}

// DeleteHost 删除主机
// 遵循模块化规范：可复用的主机删除功能
func (io *ItemOperations) DeleteHost(host *data.HostInfo, onConfirm func()) {
	if host == nil {
		ShowErrorDialog("删除失败", fmt.Errorf("主机信息为空"), io.window)
		return
	}

	// 使用统一的删除确认对话框
	ShowDeleteConfirmDialog("主机", host.Name, onConfirm, io.window)
}

// DeleteGroup 删除主机组
// 遵循模块化规范：可复用的主机组删除功能
func (io *ItemOperations) DeleteGroup(group *data.HostGroup, onConfirm func()) {
	if group == nil {
		ShowErrorDialog("删除失败", fmt.Errorf("主机组信息为空"), io.window)
		return
	}

	// 使用统一的删除确认对话框
	ShowDeleteConfirmDialog("主机组", group.Name, onConfirm, io.window)
}

// TestHostConnection 测试主机连接
// 遵循模块化规范：可复用的连接测试功能
func (io *ItemOperations) TestHostConnection(host *data.HostInfo, testFunc func(*data.HostInfo)) {
	if host == nil {
		ShowErrorDialog("测试失败", fmt.Errorf("主机信息为空"), io.window)
		return
	}

	if testFunc == nil {
		ShowErrorDialog("测试失败", fmt.Errorf("测试函数未定义"), io.window)
		return
	}

	// 委托给具体的测试函数
	testFunc(host)
}

// TestGroupConnections 测试主机组内所有主机的连接
// 遵循模块化规范：可复用的批量连接测试功能，显示详细结果
func (io *ItemOperations) TestGroupConnections(group *data.HostGroup, testFunc func(*data.HostInfo) *ConnectionTestResult) {
	if group == nil {
		ShowErrorDialog("测试失败", fmt.Errorf("主机组信息为空"), io.window)
		return
	}

	if len(group.Hosts) == 0 {
		ShowErrorDialog("测试失败", fmt.Errorf("主机组 '%s' 中没有主机", group.Name), io.window)
		return
	}

	if testFunc == nil {
		ShowErrorDialog("测试失败", fmt.Errorf("测试函数未定义"), io.window)
		return
	}

	// 显示批量测试确认对话框
	message := fmt.Sprintf("将要测试主机组 '%s' 中的 %d 台主机的连接\n\n是否继续？", group.Name, len(group.Hosts))
	ShowConfirmDialog("批量连接测试", message, func() {
		// 执行批量测试并收集结果
		io.performGroupConnectionTest(group, testFunc)
	}, io.window)
}

// performGroupConnectionTest 执行主机组连接测试并显示结果
func (io *ItemOperations) performGroupConnectionTest(group *data.HostGroup, testFunc func(*data.HostInfo) *ConnectionTestResult) {
	var results []HostConnectionResult

	// 逐个测试主机组中的主机
	for _, host := range group.Hosts {
		// 调用测试函数
		testResult := testFunc(&host)

		// 转换为显示结果
		result := HostConnectionResult{
			HostName: host.Name,
			HostAddr: host.Host,
			HostPort: host.Port,
			Success:  testResult.Status == "连接成功",
			Duration: testResult.Duration,
		}

		if !result.Success {
			result.Error = testResult.Error
		}

		results = append(results, result)
	}

	// 显示测试结果对话框
	ShowGroupConnectionTestDialog(group.Name, results, io.window)
}

// ConnectionTestResult 连接测试结果（需要与服务层的结果结构匹配）
type ConnectionTestResult struct {
	Status   string
	Error    string
	Duration time.Duration
}

// AddHost 添加主机
// 遵循模块化规范：可复用的主机添加功能
func (io *ItemOperations) AddHost(onSave func(name, hostAddr, username, password string, port int)) {
	// 使用统一的主机表单对话框
	CreateHostFormDialog("添加主机", nil, onSave, io.window)
}

// AddGroup 添加主机组
// 遵循模块化规范：可复用的主机组添加功能，使用你原来调好的完整对话框
func (io *ItemOperations) AddGroup(onSave func(name, description string, hosts []GroupHostInfo)) {
	// 使用你原来调好的完整主机组对话框（ShowAddGroupWithHostsDialog）
	ShowAddGroupWithHostsDialog(func(groupName, groupDesc string, hosts []GroupHostInfo) {
		// 传递完整的信息，包括组内主机
		onSave(groupName, groupDesc, hosts)
	}, io.window)
}

// ShowItemInfo 显示项目信息
// 遵循模块化规范：可复用的信息显示功能
func (io *ItemOperations) ShowItemInfo(title, message string) {
	ShowSuccessDialog(title, message, io.window)
}

// ShowItemError 显示项目错误
// 遵循模块化规范：可复用的错误显示功能
func (io *ItemOperations) ShowItemError(title string, err error) {
	ShowErrorDialog(title, err, io.window)
}

// ConfirmOperation 确认操作
// 遵循模块化规范：可复用的操作确认功能
func (io *ItemOperations) ConfirmOperation(title, message string, onConfirm func()) {
	ShowConfirmDialog(title, message, onConfirm, io.window)
}

// BatchDeleteHosts 批量删除主机
// 遵循模块化规范：可复用的批量删除功能
func (io *ItemOperations) BatchDeleteHosts(hosts []data.HostInfo, onConfirm func()) {
	if len(hosts) == 0 {
		ShowErrorDialog("删除失败", fmt.Errorf("没有选择要删除的主机"), io.window)
		return
	}

	message := fmt.Sprintf("确定要删除选中的 %d 台主机吗？\n\n此操作不可撤销！", len(hosts))
	ShowConfirmDialog("批量删除主机", message, onConfirm, io.window)
}

// BatchDeleteGroups 批量删除主机组
// 遵循模块化规范：可复用的批量删除功能
func (io *ItemOperations) BatchDeleteGroups(groups []data.HostGroup, onConfirm func()) {
	if len(groups) == 0 {
		ShowErrorDialog("删除失败", fmt.Errorf("没有选择要删除的主机组"), io.window)
		return
	}

	message := fmt.Sprintf("确定要删除选中的 %d 个主机组吗？\n\n此操作不可撤销！", len(groups))
	ShowConfirmDialog("批量删除主机组", message, onConfirm, io.window)
}

// ExportItems 导出项目
// 遵循模块化规范：可复用的导出功能
func (io *ItemOperations) ExportItems(itemType string, count int, exportFunc func()) {
	if count == 0 {
		ShowErrorDialog("导出失败", fmt.Errorf("没有%s数据可导出", itemType), io.window)
		return
	}

	message := fmt.Sprintf("将要导出 %d 个%s的配置信息\n\n是否继续？", count, itemType)
	ShowConfirmDialog(fmt.Sprintf("导出%s", itemType), message, exportFunc, io.window)
}

// ImportItems 导入项目
// 遵循模块化规范：可复用的导入功能
func (io *ItemOperations) ImportItems(itemType string, importFunc func()) {
	message := fmt.Sprintf("将要导入%s配置文件\n\n请选择要导入的文件", itemType)
	ShowConfirmDialog(fmt.Sprintf("导入%s", itemType), message, importFunc, io.window)
}
