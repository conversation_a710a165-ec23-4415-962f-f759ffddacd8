package components

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
	"lcheck/utils"
)

// ScanTable 扫描任务表格组件 - 专门负责任务列表的显示和交互
type ScanTable struct {
	table         *widget.Table
	container     *fyne.Container
	tasks         []data.ScanTask
	selectedTask  *data.ScanTask
	onSelection   func(*data.ScanTask)
	onDoubleClick func(*data.ScanTask)
}

// NewScanTable 创建扫描任务表格
func NewScanTable() *ScanTable {
	st := &ScanTable{
		tasks: make([]data.ScanTask, 0),
	}
	
	st.buildTable()
	st.container = container.NewBorder(nil, nil, nil, nil, st.table)
	
	return st
}

// buildTable 构建表格
func (st *ScanTable) buildTable() {
	st.table = widget.NewTable(
		func() (int, int) {
			return len(st.tasks), 6 // 6列：名称、状态、进度、主机数、创建时间、操作
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("")
		},
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			label := obj.(*widget.Label)
			
			if id.Row >= len(st.tasks) {
				label.SetText("")
				return
			}
			
			task := st.tasks[id.Row]
			
			switch id.Col {
			case 0: // 任务名称
				label.SetText(task.Name)
			case 1: // 状态
				label.SetText(task.Status)
				// 根据状态设置颜色
				switch task.Status {
				case "运行中":
					label.Importance = widget.MediumImportance
				case "已完成":
					label.Importance = widget.SuccessImportance
				case "失败":
					label.Importance = widget.DangerImportance
				default:
					label.Importance = widget.LowImportance
				}
			case 2: // 进度
				label.SetText(fmt.Sprintf("%.1f%%", task.Progress))
			case 3: // 主机数量
				label.SetText(fmt.Sprintf("%d", len(task.HostIDs)))
			case 4: // 创建时间
				label.SetText(utils.FormatTimeShort(task.CreatedAt))
			case 5: // 持续时间
				if task.Status == "运行中" {
					duration := time.Since(task.CreatedAt)
					label.SetText(utils.FormatDuration(duration))
				} else if task.CompletedAt != nil {
					duration := task.CompletedAt.Sub(task.CreatedAt)
					label.SetText(utils.FormatDuration(duration))
				} else {
					label.SetText("-")
				}
			}
		},
	)
	
	// 设置列宽
	st.table.SetColumnWidth(0, 200) // 任务名称
	st.table.SetColumnWidth(1, 80)  // 状态
	st.table.SetColumnWidth(2, 80)  // 进度
	st.table.SetColumnWidth(3, 80)  // 主机数量
	st.table.SetColumnWidth(4, 120) // 创建时间
	st.table.SetColumnWidth(5, 100) // 持续时间
	
	// 设置选择回调
	st.table.OnSelected = func(id widget.TableCellID) {
		if id.Row >= 0 && id.Row < len(st.tasks) {
			st.selectedTask = &st.tasks[id.Row]
			if st.onSelection != nil {
				st.onSelection(st.selectedTask)
			}
		}
	}
	
	// 设置双击回调
	st.table.OnUnselected = func(id widget.TableCellID) {
		st.selectedTask = nil
		if st.onSelection != nil {
			st.onSelection(nil)
		}
	}
}

// GetContainer 获取容器
func (st *ScanTable) GetContainer() *fyne.Container {
	return st.container
}

// SetTasks 设置任务列表
func (st *ScanTable) SetTasks(tasks []data.ScanTask) {
	st.tasks = tasks
	st.selectedTask = nil
	st.table.Refresh()
}

// GetTasks 获取任务列表
func (st *ScanTable) GetTasks() []data.ScanTask {
	return st.tasks
}

// GetSelectedTask 获取选中的任务
func (st *ScanTable) GetSelectedTask() *data.ScanTask {
	return st.selectedTask
}

// SetSelectedTask 设置选中的任务
func (st *ScanTable) SetSelectedTask(task *data.ScanTask) {
	st.selectedTask = task
	
	// 在表格中选中对应行
	for i, t := range st.tasks {
		if task != nil && t.ID == task.ID {
			st.table.Select(widget.TableCellID{Row: i, Col: 0})
			return
		}
	}
	
	// 如果没找到，取消选择
	st.table.UnselectAll()
}

// SetOnSelection 设置选择回调
func (st *ScanTable) SetOnSelection(callback func(*data.ScanTask)) {
	st.onSelection = callback
}

// SetOnDoubleClick 设置双击回调
func (st *ScanTable) SetOnDoubleClick(callback func(*data.ScanTask)) {
	st.onDoubleClick = callback
}

// AddTask 添加任务
func (st *ScanTable) AddTask(task data.ScanTask) {
	st.tasks = append(st.tasks, task)
	st.table.Refresh()
}

// UpdateTask 更新任务
func (st *ScanTable) UpdateTask(task data.ScanTask) {
	for i, t := range st.tasks {
		if t.ID == task.ID {
			st.tasks[i] = task
			st.table.Refresh()
			return
		}
	}
}

// RemoveTask 移除任务
func (st *ScanTable) RemoveTask(taskID string) {
	for i, task := range st.tasks {
		if task.ID == taskID {
			st.tasks = append(st.tasks[:i], st.tasks[i+1:]...)
			st.selectedTask = nil
			st.table.Refresh()
			return
		}
	}
}

// ClearTasks 清空任务列表
func (st *ScanTable) ClearTasks() {
	st.tasks = make([]data.ScanTask, 0)
	st.selectedTask = nil
	st.table.Refresh()
}

// RefreshTable 刷新表格
func (st *ScanTable) RefreshTable() {
	st.table.Refresh()
}

// GetTaskCount 获取任务数量
func (st *ScanTable) GetTaskCount() int {
	return len(st.tasks)
}

// GetRunningTaskCount 获取运行中的任务数量
func (st *ScanTable) GetRunningTaskCount() int {
	count := 0
	for _, task := range st.tasks {
		if task.Status == "运行中" {
			count++
		}
	}
	return count
}

// GetCompletedTaskCount 获取已完成的任务数量
func (st *ScanTable) GetCompletedTaskCount() int {
	count := 0
	for _, task := range st.tasks {
		if task.Status == "已完成" {
			count++
		}
	}
	return count
}

// GetFailedTaskCount 获取失败的任务数量
func (st *ScanTable) GetFailedTaskCount() int {
	count := 0
	for _, task := range st.tasks {
		if task.Status == "失败" {
			count++
		}
	}
	return count
}

// FilterTasks 过滤任务
func (st *ScanTable) FilterTasks(filter func(data.ScanTask) bool) {
	var filteredTasks []data.ScanTask
	for _, task := range st.tasks {
		if filter(task) {
			filteredTasks = append(filteredTasks, task)
		}
	}
	
	originalTasks := st.tasks
	st.tasks = filteredTasks
	st.table.Refresh()
	
	// 如果当前选中的任务不在过滤结果中，清除选择
	if st.selectedTask != nil {
		found := false
		for _, task := range filteredTasks {
			if task.ID == st.selectedTask.ID {
				found = true
				break
			}
		}
		if !found {
			st.selectedTask = nil
			if st.onSelection != nil {
				st.onSelection(nil)
			}
		}
	}
	
	// 保存原始任务列表的引用，以便后续恢复
	st.tasks = originalTasks
}

// SortTasks 排序任务
func (st *ScanTable) SortTasks(sortBy string, ascending bool) {
	// 这里可以实现各种排序逻辑
	// 为简化，暂时不实现具体排序
	st.table.Refresh()
}

// ExportTasks 导出任务数据
func (st *ScanTable) ExportTasks() []data.ScanTask {
	// 返回任务列表的副本
	exportTasks := make([]data.ScanTask, len(st.tasks))
	copy(exportTasks, st.tasks)
	return exportTasks
}
