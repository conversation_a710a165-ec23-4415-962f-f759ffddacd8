package components

import "fyne.io/fyne/v2"

// UI常量配置文件
// 遵循模块化规范：避免硬编码，统一UI尺寸和样式配置

// 列表组件尺寸常量
const (
	// 主机列表和主机组列表的统一尺寸
	ListWidth  = 400  // 列表宽度
	ListHeight = 400  // 列表高度
	
	// 对话框尺寸常量
	DialogWidth  = 300  // 对话框宽度
	DialogHeight = 405  // 对话框高度
	
	// 工具栏尺寸常量
	ToolbarHeight = 40   // 工具栏高度
	ButtonWidth   = 80   // 按钮宽度
	ButtonHeight  = 30   // 按钮高度
	
	// 输入框尺寸常量
	EntryWidth  = 250   // 输入框宽度
	EntryHeight = 30    // 输入框高度
	
	// 间距常量
	PaddingSmall  = 5   // 小间距
	PaddingMedium = 10  // 中等间距
	PaddingLarge  = 20  // 大间距
)

// UI尺寸函数
// 遵循模块化规范：提供统一的尺寸创建函数

// GetListSize 获取列表组件的标准尺寸
func GetListSize() fyne.Size {
	return fyne.NewSize(ListWidth, ListHeight)
}

// GetDialogSize 获取对话框的标准尺寸
func GetDialogSize() fyne.Size {
	return fyne.NewSize(DialogWidth, DialogHeight)
}

// GetButtonSize 获取按钮的标准尺寸
func GetButtonSize() fyne.Size {
	return fyne.NewSize(ButtonWidth, ButtonHeight)
}

// GetEntrySize 获取输入框的标准尺寸
func GetEntrySize() fyne.Size {
	return fyne.NewSize(EntryWidth, EntryHeight)
}

// UI样式常量
const (
	// 文本样式
	TitleFontSize   = 16  // 标题字体大小
	ContentFontSize = 12  // 内容字体大小
	
	// 颜色主题（可以根据需要扩展）
	PrimaryColor   = "#2196F3"  // 主色调
	SecondaryColor = "#FFC107"  // 辅助色
	SuccessColor   = "#4CAF50"  // 成功色
	ErrorColor     = "#F44336"  // 错误色
	WarningColor   = "#FF9800"  // 警告色
)

// 布局配置常量
const (
	// 分割面板配置
	HSplitOffset = 0.5  // 水平分割面板的偏移量（左右各50%）
	VSplitOffset = 0.3  // 垂直分割面板的偏移量
	
	// 边框配置
	BorderWidth = 1     // 边框宽度
	
	// 圆角配置
	CornerRadius = 4    // 圆角半径
)

// 动画配置常量
const (
	// 动画时长（毫秒）
	AnimationDurationShort  = 150   // 短动画
	AnimationDurationMedium = 300   // 中等动画
	AnimationDurationLong   = 500   // 长动画
)

// 数据显示配置常量
const (
	// 列表显示配置
	MaxDisplayItems = 100   // 列表最大显示项目数
	
	// 文本截断配置
	MaxTextLength = 50      // 文本最大长度
	
	// 分页配置
	DefaultPageSize = 20    // 默认分页大小
)

// 网络配置常量
const (
	// 连接超时配置
	ConnectionTimeout = 5   // 连接超时时间（秒）
	
	// 默认端口配置
	DefaultSSHPort = "22"   // 默认SSH端口
	DefaultHTTPPort = "80"  // 默认HTTP端口
	DefaultHTTPSPort = "443" // 默认HTTPS端口
)

// 文件配置常量
const (
	// 配置文件路径
	ConfigFileName = "config.json"      // 配置文件名
	DataFileName   = "data.json"        // 数据文件名
	LogFileName    = "app.log"          // 日志文件名
	
	// 文件大小限制
	MaxFileSize = 10 * 1024 * 1024      // 最大文件大小（10MB）
)

// 验证配置常量
const (
	// 输入验证
	MinNameLength = 1       // 最小名称长度
	MaxNameLength = 50      // 最大名称长度
	MinPasswordLength = 1   // 最小密码长度
	MaxPasswordLength = 100 // 最大密码长度
	
	// IP地址验证
	IPv4Pattern = `^(\d{1,3}\.){3}\d{1,3}$`  // IPv4正则表达式
	
	// 端口验证
	MinPort = 1             // 最小端口号
	MaxPort = 65535         // 最大端口号
)

// 错误消息常量
const (
	// 通用错误消息
	ErrInvalidInput    = "输入参数无效"
	ErrConnectionFailed = "连接失败"
	ErrFileNotFound    = "文件不存在"
	ErrPermissionDenied = "权限不足"
	
	// 主机相关错误消息
	ErrHostNotFound     = "主机不存在"
	ErrHostAlreadyExists = "主机已存在"
	ErrInvalidHostInfo  = "主机信息无效"
	
	// 主机组相关错误消息
	ErrGroupNotFound     = "主机组不存在"
	ErrGroupAlreadyExists = "主机组已存在"
	ErrInvalidGroupInfo  = "主机组信息无效"
)

// 成功消息常量
const (
	// 操作成功消息
	MsgCreateSuccess = "创建成功"
	MsgUpdateSuccess = "更新成功"
	MsgDeleteSuccess = "删除成功"
	MsgImportSuccess = "导入成功"
	MsgExportSuccess = "导出成功"
	
	// 连接成功消息
	MsgConnectionSuccess = "连接成功"
	MsgTestSuccess      = "测试成功"
)

// 确认消息常量
const (
	// 删除确认消息
	MsgConfirmDelete     = "确定要删除吗？此操作不可撤销！"
	MsgConfirmDeleteHost = "确定要删除主机 '%s' 吗？"
	MsgConfirmDeleteGroup = "确定要删除主机组 '%s' 吗？"
	
	// 覆盖确认消息
	MsgConfirmOverwrite = "文件已存在，是否覆盖？"
)

// 状态消息常量
const (
	// 加载状态消息
	MsgLoading      = "正在加载..."
	MsgSaving       = "正在保存..."
	MsgConnecting   = "正在连接..."
	MsgTesting      = "正在测试..."
	
	// 完成状态消息
	MsgLoadComplete = "加载完成"
	MsgSaveComplete = "保存完成"
	MsgReady        = "就绪"
)
