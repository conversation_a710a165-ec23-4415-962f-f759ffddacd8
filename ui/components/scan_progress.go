package components

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
	"lcheck/utils"
)

// ScanProgress 扫描进度组件 - 专门负责显示扫描进度和状态
type ScanProgress struct {
	container       *fyne.Container
	progressBar     *widget.ProgressBar
	statusLabel     *widget.Label
	detailLabel     *widget.Label
	timeLabel       *widget.Label
	hostCountLabel  *widget.Label
	
	currentTask     *data.ScanTask
	startTime       time.Time
	updateTicker    *time.Ticker
	onProgressUpdate func(float64)
}

// NewScanProgress 创建扫描进度组件
func NewScanProgress() *ScanProgress {
	sp := &ScanProgress{}
	sp.buildProgress()
	return sp
}

// buildProgress 构建进度组件
func (sp *ScanProgress) buildProgress() {
	// 创建进度条
	sp.progressBar = widget.NewProgressBar()
	sp.progressBar.SetValue(0)
	
	// 创建状态标签
	sp.statusLabel = widget.NewLabel("就绪")
	sp.statusLabel.Importance = widget.MediumImportance
	
	// 创建详细信息标签
	sp.detailLabel = widget.NewLabel("等待任务...")
	
	// 创建时间标签
	sp.timeLabel = widget.NewLabel("用时: 00:00:00")
	
	// 创建主机数量标签
	sp.hostCountLabel = widget.NewLabel("主机: 0/0")
	
	// 创建信息容器
	infoContainer := container.NewHBox(
		sp.statusLabel,
		widget.NewSeparator(),
		sp.hostCountLabel,
		widget.NewSeparator(),
		sp.timeLabel,
	)
	
	// 创建主容器
	sp.container = container.NewVBox(
		sp.progressBar,
		infoContainer,
		sp.detailLabel,
	)
}

// GetContainer 获取容器
func (sp *ScanProgress) GetContainer() *fyne.Container {
	return sp.container
}

// StartProgress 开始进度显示
func (sp *ScanProgress) StartProgress(task *data.ScanTask) {
	sp.currentTask = task
	sp.startTime = time.Now()
	
	// 重置进度
	sp.progressBar.SetValue(0)
	sp.statusLabel.SetText("启动中...")
	sp.statusLabel.Importance = widget.MediumImportance
	sp.detailLabel.SetText(fmt.Sprintf("正在启动任务: %s", task.Name))
	sp.hostCountLabel.SetText(fmt.Sprintf("主机: 0/%d", len(task.HostIDs)))
	sp.timeLabel.SetText("用时: 00:00:00")
	
	// 启动定时器更新时间显示
	if sp.updateTicker != nil {
		sp.updateTicker.Stop()
	}
	sp.updateTicker = time.NewTicker(time.Second)
	go sp.updateTimeDisplay()
}

// UpdateProgress 更新进度
func (sp *ScanProgress) UpdateProgress(progress float64, status, detail string, completedHosts int) {
	if sp.currentTask == nil {
		return
	}
	
	// 更新进度条
	sp.progressBar.SetValue(progress / 100.0)
	
	// 更新状态
	sp.statusLabel.SetText(status)
	switch status {
	case "运行中":
		sp.statusLabel.Importance = widget.MediumImportance
	case "已完成":
		sp.statusLabel.Importance = widget.SuccessImportance
	case "失败":
		sp.statusLabel.Importance = widget.DangerImportance
	case "已停止":
		sp.statusLabel.Importance = widget.WarningImportance
	default:
		sp.statusLabel.Importance = widget.LowImportance
	}
	
	// 更新详细信息
	sp.detailLabel.SetText(detail)
	
	// 更新主机计数
	totalHosts := len(sp.currentTask.HostIDs)
	sp.hostCountLabel.SetText(fmt.Sprintf("主机: %d/%d", completedHosts, totalHosts))
	
	// 触发进度更新回调
	if sp.onProgressUpdate != nil {
		sp.onProgressUpdate(progress)
	}
}

// CompleteProgress 完成进度显示
func (sp *ScanProgress) CompleteProgress(success bool, message string) {
	if sp.updateTicker != nil {
		sp.updateTicker.Stop()
		sp.updateTicker = nil
	}
	
	// 设置最终状态
	sp.progressBar.SetValue(1.0)
	
	if success {
		sp.statusLabel.SetText("已完成")
		sp.statusLabel.Importance = widget.SuccessImportance
		sp.detailLabel.SetText(message)
	} else {
		sp.statusLabel.SetText("失败")
		sp.statusLabel.Importance = widget.DangerImportance
		sp.detailLabel.SetText(fmt.Sprintf("任务失败: %s", message))
	}
	
	// 更新最终时间
	if sp.currentTask != nil {
		duration := time.Since(sp.startTime)
		sp.timeLabel.SetText(fmt.Sprintf("用时: %s", utils.FormatDuration(duration)))
	}
}

// StopProgress 停止进度显示
func (sp *ScanProgress) StopProgress() {
	if sp.updateTicker != nil {
		sp.updateTicker.Stop()
		sp.updateTicker = nil
	}
	
	sp.statusLabel.SetText("已停止")
	sp.statusLabel.Importance = widget.WarningImportance
	sp.detailLabel.SetText("任务已被用户停止")
	
	if sp.currentTask != nil {
		duration := time.Since(sp.startTime)
		sp.timeLabel.SetText(fmt.Sprintf("用时: %s", utils.FormatDuration(duration)))
	}
}

// ResetProgress 重置进度显示
func (sp *ScanProgress) ResetProgress() {
	if sp.updateTicker != nil {
		sp.updateTicker.Stop()
		sp.updateTicker = nil
	}
	
	sp.currentTask = nil
	sp.progressBar.SetValue(0)
	sp.statusLabel.SetText("就绪")
	sp.statusLabel.Importance = widget.MediumImportance
	sp.detailLabel.SetText("等待任务...")
	sp.timeLabel.SetText("用时: 00:00:00")
	sp.hostCountLabel.SetText("主机: 0/0")
}

// updateTimeDisplay 更新时间显示
func (sp *ScanProgress) updateTimeDisplay() {
	if sp.updateTicker == nil {
		return
	}
	
	for range sp.updateTicker.C {
		if sp.currentTask == nil {
			break
		}
		
		duration := time.Since(sp.startTime)
		sp.timeLabel.SetText(fmt.Sprintf("用时: %s", utils.FormatDuration(duration)))
	}
}

// SetOnProgressUpdate 设置进度更新回调
func (sp *ScanProgress) SetOnProgressUpdate(callback func(float64)) {
	sp.onProgressUpdate = callback
}

// GetCurrentTask 获取当前任务
func (sp *ScanProgress) GetCurrentTask() *data.ScanTask {
	return sp.currentTask
}

// GetProgress 获取当前进度
func (sp *ScanProgress) GetProgress() float64 {
	return sp.progressBar.Value * 100.0
}

// GetStatus 获取当前状态
func (sp *ScanProgress) GetStatus() string {
	return sp.statusLabel.Text
}

// GetDetail 获取详细信息
func (sp *ScanProgress) GetDetail() string {
	return sp.detailLabel.Text
}

// GetElapsedTime 获取已用时间
func (sp *ScanProgress) GetElapsedTime() time.Duration {
	if sp.currentTask == nil {
		return 0
	}
	return time.Since(sp.startTime)
}

// IsRunning 检查是否正在运行
func (sp *ScanProgress) IsRunning() bool {
	return sp.currentTask != nil && sp.updateTicker != nil
}

// SetProgressBarColor 设置进度条颜色（如果支持）
func (sp *ScanProgress) SetProgressBarColor(color string) {
	// Fyne默认不支持自定义进度条颜色
	// 这里预留接口，以备将来扩展
}

// ShowIndeterminate 显示不确定进度
func (sp *ScanProgress) ShowIndeterminate(show bool) {
	// Fyne的ProgressBar不直接支持不确定模式
	// 可以通过动画效果模拟
	if show {
		// 可以实现一个简单的动画效果
		go func() {
			for i := 0; i < 100 && sp.IsRunning(); i++ {
				sp.progressBar.SetValue(float64(i) / 100.0)
				time.Sleep(50 * time.Millisecond)
			}
		}()
	}
}

// Cleanup 清理资源
func (sp *ScanProgress) Cleanup() {
	if sp.updateTicker != nil {
		sp.updateTicker.Stop()
		sp.updateTicker = nil
	}
}
