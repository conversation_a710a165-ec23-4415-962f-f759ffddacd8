package components

import (
	"fmt"
	"strconv"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/storage"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
)

// GroupHostInfo 组内主机信息结构
type GroupHostInfo struct {
	NameEntry *widget.Entry
	AddrEntry *widget.Entry
	UserEntry *widget.Entry
	PassEntry *widget.Entry
	Container *fyne.Container
}

// ShowSuccessDialog 显示成功对话框
func ShowSuccessDialog(title, message string, parent fyne.Window) {
	dialog.ShowInformation(title, message, parent)
}

// ShowErrorDialog 显示错误对话框
func ShowErrorDialog(title string, err error, parent fyne.Window) {
	dialog.ShowError(err, parent)
}

// ShowConfirmDialog 显示确认对话框
// 遵循模块化规范：可复用的确认对话框组件
func ShowConfirmDialog(title, message string, onConfirm func(), parent fyne.Window) {
	dialog.ShowConfirm(title, message, func(confirmed bool) {
		if confirmed && onConfirm != nil {
			onConfirm()
		}
	}, parent)
}

// ShowDeleteConfirmDialog 显示删除确认对话框
// 遵循模块化规范：专门的删除确认组件，可复用于主机、主机组等
func ShowDeleteConfirmDialog(itemType, itemName string, onConfirm func(), parent fyne.Window) {
	title := fmt.Sprintf("确认删除%s", itemType)
	message := fmt.Sprintf("确定要删除%s '%s' 吗？\n\n此操作不可撤销！", itemType, itemName)

	dialog.ShowConfirm(title, message, func(confirmed bool) {
		if confirmed && onConfirm != nil {
			onConfirm()
		}
	}, parent)
}

// CreateHostFormDialog 创建主机表单对话框
func CreateHostFormDialog(title string, host *data.HostInfo, onSave func(name, hostAddr, username, password string, port int), parent fyne.Window) {
	// 创建表单字段，设置统一的大小
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("输入主机名称")

	hostEntry := widget.NewEntry()
	hostEntry.SetPlaceHolder("输入主机地址或IP")

	portEntry := widget.NewEntry()
	portEntry.SetText("22")
	portEntry.SetPlaceHolder("SSH端口")

	usernameEntry := widget.NewEntry()
	usernameEntry.SetPlaceHolder("SSH用户名")

	// 连接方式选择
	authTypeSelect := widget.NewSelect([]string{"密码连接", "密钥连接"}, nil)
	authTypeSelect.SetSelected("密码连接")

	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.SetPlaceHolder("SSH密码")

	keyFileEntry := widget.NewEntry()
	keyFileEntry.SetPlaceHolder("私钥文件路径")
	keyFileEntry.Hide() // 默认隐藏

	browseBtn := widget.NewButton("浏览", func() {
		// 创建文件选择对话框
		fileDialog := dialog.NewFileOpen(func(reader fyne.URIReadCloser, err error) {
			if err != nil {
				return
			}
			if reader != nil {
				keyFileEntry.SetText(reader.URI().Path())
				reader.Close()
			}
		}, parent)

		// 设置文件过滤器，只显示常见的密钥文件
		fileDialog.SetFilter(storage.NewExtensionFileFilter([]string{".pem", ".key", ".pub", ".ppk"}))
		fileDialog.Show()
	})
	// 不要单独隐藏browseBtn，让keyContainer控制显示

	keyContainer := container.NewBorder(nil, nil, nil, browseBtn, keyFileEntry)

	// 密钥密码输入框（用于有密码保护的密钥文件）
	keyPassEntry := widget.NewPasswordEntry()
	keyPassEntry.SetPlaceHolder("密钥密码（可选）")
	// 不要单独隐藏keyPassEntry，让keyForm控制显示

	// 如果是编辑模式，填充现有数据
	if host != nil {
		nameEntry.SetText(host.Name)
		hostEntry.SetText(host.Host)
		portEntry.SetText(host.Port) // host.Port已经是字符串
		usernameEntry.SetText(host.Username)
		passwordEntry.SetText(host.Password)
	}

	// 创建动态表单容器
	var currentForm *widget.Form
	formContainer := container.NewVBox()

	// 创建密码模式表单
	createPasswordForm := func() {
		currentForm = widget.NewForm(
			widget.NewFormItem("主机名称", nameEntry),
			widget.NewFormItem("主机地址", hostEntry),
			widget.NewFormItem("SSH端口", portEntry),
			widget.NewFormItem("用户名", usernameEntry),
			widget.NewFormItem("连接方式", authTypeSelect),
			widget.NewFormItem("密码", passwordEntry),
		)
		formContainer.RemoveAll()
		formContainer.Add(currentForm)
	}

	// 创建密钥模式表单
	createKeyForm := func() {
		currentForm = widget.NewForm(
			widget.NewFormItem("主机名称", nameEntry),
			widget.NewFormItem("主机地址", hostEntry),
			widget.NewFormItem("SSH端口", portEntry),
			widget.NewFormItem("用户名", usernameEntry),
			widget.NewFormItem("连接方式", authTypeSelect),
			widget.NewFormItem("密钥文件", keyContainer),
			widget.NewFormItem("密钥密码", keyPassEntry),
		)
		formContainer.RemoveAll()
		formContainer.Add(currentForm)
	}

	// 默认创建密码表单
	createPasswordForm()

	// 连接方式切换逻辑
	authTypeSelect.OnChanged = func(selected string) {
		if selected == "密钥连接" {
			createKeyForm()
		} else {
			createPasswordForm()
		}
	}

	// 创建自定义对话框
	dialog := dialog.NewCustomConfirm(title, "保存", "取消", formContainer, func(confirmed bool) {
		if !confirmed {
			return
		}

		// 验证输入
		name := nameEntry.Text
		hostAddr := hostEntry.Text
		username := usernameEntry.Text
		authType := authTypeSelect.Selected
		password := passwordEntry.Text
		keyFile := keyFileEntry.Text
		keyPass := keyPassEntry.Text

		if name == "" || hostAddr == "" || username == "" {
			ShowErrorDialog("输入错误", fmt.Errorf("主机名称、地址和用户名不能为空"), parent)
			return
		}

		// 根据连接方式验证
		if authType == "密码连接" {
			if password == "" {
				ShowErrorDialog("输入错误", fmt.Errorf("密码不能为空"), parent)
				return
			}
		} else if authType == "密钥连接" {
			if keyFile == "" {
				ShowErrorDialog("输入错误", fmt.Errorf("密钥文件路径不能为空"), parent)
				return
			}
			// 对于密钥连接，密码字段用于存储密钥信息（文件路径和密钥密码）
			if keyPass != "" {
				password = "KEY:" + keyFile + "|PASS:" + keyPass
			} else {
				password = "KEY:" + keyFile
			}
		}

		port, err := strconv.Atoi(portEntry.Text)
		if err != nil || port < 1 || port > 65535 {
			ShowErrorDialog("输入错误", fmt.Errorf("端口号必须是1-65535之间的数字"), parent)
			return
		}

		onSave(name, hostAddr, username, password, port)
	}, parent)

	// 设置对话框大小 - 宽度300px，高度405px
	dialog.Resize(fyne.NewSize(300, 405))
	dialog.Show()
}

// CreateGroupFormDialog 创建主机组表单对话框 - 简化版本
func CreateGroupFormDialog(title string, group *data.HostGroup, hosts []data.HostInfo, onSave func(name, description string, hostIDs []string), parent fyne.Window) {
	// 创建表单字段
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("输入主机组名称")

	descEntry := widget.NewMultiLineEntry()
	descEntry.SetPlaceHolder("输入主机组描述（可选）")

	// 如果是编辑模式，填充现有数据
	if group != nil {
		nameEntry.SetText(group.Name)
		descEntry.SetText(group.Description)
	}

	// 创建表单
	form := &widget.Form{
		Items: []*widget.FormItem{
			{Text: "主机组名称:", Widget: nameEntry},
			{Text: "描述:", Widget: descEntry},
		},
		OnSubmit: func() {
			name := nameEntry.Text
			description := descEntry.Text

			if name == "" {
				ShowErrorDialog("输入错误", fmt.Errorf("主机组名称不能为空"), parent)
				return
			}

			// 暂时返回空的主机ID列表，后续可以扩展
			onSave(name, description, []string{})
		},
	}

	// 显示对话框
	dialog.ShowForm(title, "保存", "取消", form.Items, func(confirmed bool) {
		if confirmed {
			form.OnSubmit()
		}
	}, parent)
}

// CreateTaskFormDialog 创建任务表单对话框 - 简化版本
func CreateTaskFormDialog(hosts []data.HostInfo, groups []data.HostGroup, onSave func(name string, hostIDs []string, concurrency int), parent fyne.Window) {
	// 创建表单字段
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("输入任务名称")

	concurrencyEntry := widget.NewEntry()
	concurrencyEntry.SetText("3")
	concurrencyEntry.SetPlaceHolder("并发数 (1-10)")

	// 创建表单
	form := &widget.Form{
		Items: []*widget.FormItem{
			{Text: "任务名称:", Widget: nameEntry},
			{Text: "并发数:", Widget: concurrencyEntry},
		},
		OnSubmit: func() {
			name := nameEntry.Text
			if name == "" {
				ShowErrorDialog("输入错误", fmt.Errorf("任务名称不能为空"), parent)
				return
			}

			concurrency, err := strconv.Atoi(concurrencyEntry.Text)
			if err != nil || concurrency < 1 || concurrency > 10 {
				ShowErrorDialog("输入错误", fmt.Errorf("并发数必须是1-10之间的数字"), parent)
				return
			}

			// 暂时使用所有主机ID，后续可以扩展选择功能
			var hostIDs []string
			for _, host := range hosts {
				hostIDs = append(hostIDs, host.ID)
			}

			if len(hostIDs) == 0 {
				ShowErrorDialog("选择错误", fmt.Errorf("没有可用的主机"), parent)
				return
			}

			onSave(name, hostIDs, concurrency)
		},
	}

	// 显示对话框
	dialog.ShowForm("创建扫描任务", "创建", "取消", form.Items, func(confirmed bool) {
		if confirmed {
			form.OnSubmit()
		}
	}, parent)
}

// ShowAddGroupWithHostsDialog 显示添加主机组对话框（组内新建主机）
func ShowAddGroupWithHostsDialog(onSave func(groupName, groupDesc string, hosts []GroupHostInfo), parent fyne.Window) {
	ShowGroupWithHostsDialog("添加主机组", nil, onSave, parent)
}

// ShowEditGroupWithHostsDialog 显示编辑主机组对话框（组内编辑主机）
func ShowEditGroupWithHostsDialog(group *data.HostGroup, onSave func(groupName, groupDesc string, hosts []GroupHostInfo), parent fyne.Window) {
	ShowGroupWithHostsDialog("编辑主机组", group, onSave, parent)
}

// ShowGroupWithHostsDialog 显示主机组对话框（通用版本，支持添加和编辑）
func ShowGroupWithHostsDialog(title string, group *data.HostGroup, onSave func(groupName, groupDesc string, hosts []GroupHostInfo), parent fyne.Window) {
	if parent == nil {
		return
	}

	// 使用defer来捕获panic
	defer func() {
		if r := recover(); r != nil {
			ShowErrorDialog("对话框错误", fmt.Errorf("对话框创建失败: %v", r), parent)
		}
	}()

	// 创建主机组基本信息输入字段
	groupNameEntry := widget.NewEntry()
	groupNameEntry.SetPlaceHolder("主机组名称")
	groupNameEntry.Resize(fyne.NewSize(250, 25))

	// 使用单行输入框代替多行输入框，这样高度更小
	groupDescEntry := widget.NewEntry()
	groupDescEntry.SetPlaceHolder("主机组描述（可选）")
	groupDescEntry.Resize(fyne.NewSize(250, 25))

	// 如果是编辑模式，填充现有数据
	if group != nil {
		groupNameEntry.SetText(group.Name)
		groupDescEntry.SetText(group.Description)
	}

	// 创建组内主机列表容器
	var groupHosts []GroupHostInfo
	hostListContainer := container.NewVBox()

	// 如果是编辑模式，预填充现有主机
	if group != nil {
		for _, host := range group.Hosts {
			hostInfo := GroupHostInfo{
				NameEntry: widget.NewEntry(),
				AddrEntry: widget.NewEntry(),
				UserEntry: widget.NewEntry(),
				PassEntry: widget.NewEntry(),
			}

			// 设置现有主机的值
			hostInfo.NameEntry.SetText(host.Name)
			hostInfo.AddrEntry.SetText(host.Host)
			hostInfo.UserEntry.SetText(host.Username)
			hostInfo.PassEntry.SetText(host.Password)

			groupHosts = append(groupHosts, hostInfo)
		}
	}

	// 更新主机列表显示
	var updateHostList func()
	updateHostList = func() {
		hostListContainer.RemoveAll()
		for i, hostInfo := range groupHosts {
			// 获取主机信息
			hostName := hostInfo.NameEntry.Text
			hostAddr := hostInfo.AddrEntry.Text
			hostUser := hostInfo.UserEntry.Text

			// 确保信息不为空
			if hostName == "" {
				hostName = "未命名主机"
			}
			if hostAddr == "" {
				hostAddr = "未设置地址"
			}
			if hostUser == "" {
				hostUser = "未设置用户"
			}

			// 主机信息标签 - 单行显示
			infoText := fmt.Sprintf("%d. %s (%s@%s)", i+1, hostName, hostUser, hostAddr)
			hostLabel := widget.NewLabel(infoText)
			// 不设置换行，默认就是单行显示
			hostLabel.Resize(fyne.NewSize(250, 22)) // 设置固定大小

			// 删除按钮
			removeBtn := widget.NewButton("删除", nil)
			removeBtn.Resize(fyne.NewSize(45, 22))
			hostIndex := i // 捕获索引
			removeBtn.OnTapped = func() {
				// 从列表中移除
				groupHosts = append(groupHosts[:hostIndex], groupHosts[hostIndex+1:]...)
				updateHostList()
			}

			// 使用Border布局，确保按钮在右边
			hostRow := container.NewBorder(nil, nil, hostLabel, removeBtn)

			hostListContainer.Add(hostRow)
		}

		if len(groupHosts) == 0 {
			hostListContainer.Add(widget.NewLabel("暂无主机，请点击下方按钮添加"))
		}

		hostListContainer.Refresh()
	}

	// 添加主机到组的函数 - 弹出对话框
	addHostToGroup := func() {
		CreateHostFormDialog("添加主机到组", nil, func(name, hostAddr, username, password string, port int) {
			// 创建主机信息结构
			hostInfo := GroupHostInfo{
				NameEntry: widget.NewEntry(),
				AddrEntry: widget.NewEntry(),
				UserEntry: widget.NewEntry(),
				PassEntry: widget.NewEntry(),
			}

			// 设置值
			hostInfo.NameEntry.SetText(name)
			hostInfo.AddrEntry.SetText(hostAddr)
			hostInfo.UserEntry.SetText(username)
			hostInfo.PassEntry.SetText(password)

			// 添加到列表
			groupHosts = append(groupHosts, hostInfo)
			updateHostList()
		}, parent)
	}

	// 添加主机按钮
	addHostBtn := widget.NewButton("+ 添加主机到组", func() {
		addHostToGroup()
	})

	// 初始化主机列表显示
	updateHostList()

	// 主机列表滚动容器 - 设置固定高度和滚动行为
	hostScroll := container.NewScroll(hostListContainer)
	hostScroll.SetMinSize(fyne.NewSize(320, 200))

	// 创建一个固定高度的容器来包装滚动区域
	scrollWrapper := container.NewBorder(nil, nil, nil, nil, hostScroll)
	scrollWrapper.Resize(fyne.NewSize(320, 200))

	// 创建完整表单 - 使用VBox垂直布局
	form := container.NewVBox(
		// 主机组信息区域
		widget.NewLabel("主机组信息"),
		// 使用简单的Form布局
		widget.NewForm(
			widget.NewFormItem("组名称", groupNameEntry),
			widget.NewFormItem("组描述", groupDescEntry),
		),
		widget.NewSeparator(),
		// 主机列表区域
		widget.NewLabel("组内主机列表"),
		addHostBtn,
		scrollWrapper, // 固定高度的主机列表区域
	)

	// 创建对话框，使用传入的标题
	buttonText := "创建"
	if group != nil {
		buttonText = "保存"
	}
	dialog := dialog.NewCustomConfirm(title, buttonText, "取消", form, func(confirmed bool) {
		if !confirmed {
			return
		}

		// 验证主机组名称
		if groupNameEntry.Text == "" {
			ShowErrorDialog("输入错误", fmt.Errorf("主机组名称不能为空"), parent)
			return
		}

		// 验证组内主机信息
		if len(groupHosts) == 0 {
			ShowErrorDialog("输入错误", fmt.Errorf("主机组至少需要包含一个主机"), parent)
			return
		}

		// 调用保存回调
		onSave(groupNameEntry.Text, groupDescEntry.Text, groupHosts)
	}, parent)

	dialog.Resize(fyne.NewSize(350, 420))
	dialog.Show()
}

// ShowGroupConnectionTestDialog 显示主机组连接测试结果对话框
// 遵循模块化规范：可复用的测试结果显示组件
func ShowGroupConnectionTestDialog(groupName string, results []HostConnectionResult, parent fyne.Window) {
	// 创建结果列表
	resultList := widget.NewList(
		func() int {
			return len(results)
		},
		func() fyne.CanvasObject {
			// 创建一行显示：主机名 - 状态 - 详情
			nameLabel := widget.NewLabel("")
			statusLabel := widget.NewLabel("")
			detailLabel := widget.NewLabel("")

			// 设置状态标签的样式
			statusLabel.Resize(fyne.NewSize(60, 25))

			return container.NewHBox(
				nameLabel,
				widget.NewSeparator(),
				statusLabel,
				widget.NewSeparator(),
				detailLabel,
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= 0 && id < len(results) {
				result := results[id]
				container := obj.(*fyne.Container)

				nameLabel := container.Objects[0].(*widget.Label)
				statusLabel := container.Objects[2].(*widget.Label)
				detailLabel := container.Objects[4].(*widget.Label)

				// 设置主机名
				nameLabel.SetText(fmt.Sprintf("%s (%s:%s)", result.HostName, result.HostAddr, result.HostPort))

				// 设置状态和颜色
				if result.Success {
					statusLabel.SetText("✅ 成功")
					statusLabel.Importance = widget.SuccessImportance
					detailLabel.SetText(fmt.Sprintf("耗时: %v", result.Duration))
				} else {
					statusLabel.SetText("❌ 失败")
					statusLabel.Importance = widget.DangerImportance
					detailLabel.SetText(fmt.Sprintf("错误: %s", result.Error))
				}
			}
		},
	)

	// 统计结果
	successCount := 0
	failCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		} else {
			failCount++
		}
	}

	// 创建统计信息
	summaryText := fmt.Sprintf("主机组 '%s' 连接测试结果\n\n总计: %d 台主机\n✅ 成功: %d 台\n❌ 失败: %d 台",
		groupName, len(results), successCount, failCount)
	summaryLabel := widget.NewLabel(summaryText)
	summaryLabel.Wrapping = fyne.TextWrapWord

	// 创建内容容器
	content := container.NewVBox(
		summaryLabel,
		widget.NewSeparator(),
		widget.NewLabel("详细结果:"),
		container.NewScroll(resultList),
	)

	// 创建对话框
	dialog := dialog.NewCustom("主机组连接测试结果", "关闭", content, parent)
	dialog.Resize(fyne.NewSize(500, 400))
	dialog.Show()
}

// HostConnectionResult 主机连接测试结果
type HostConnectionResult struct {
	HostName string
	HostAddr string
	HostPort string
	Success  bool
	Error    string
	Duration time.Duration
}


