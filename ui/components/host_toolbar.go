package components

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
)

// HostToolbar 主机工具栏组件 - 专门负责主机管理相关的工具栏按钮
type HostToolbar struct {
	container        *fyne.Container
	addHostButton    *widget.Button
	addGroupButton   *widget.Button
	editButton       *widget.Button
	deleteButton     *widget.Button
	testButton       *widget.Button
	importButton     *widget.Button
	exportButton     *widget.Button
	refreshButton    *widget.Button
	
	// 回调函数
	onAddHost     func()
	onAddGroup    func()
	onEditItem    func()
	onDeleteItem  func()
	onTestConnection func()
	onImportData  func()
	onExportData  func()
	onRefreshData func()
}

// NewHostToolbar 创建主机工具栏
func NewHostToolbar() *HostToolbar {
	ht := &HostToolbar{}
	ht.buildToolbar()
	return ht
}

// buildToolbar 构建工具栏
func (ht *HostToolbar) buildToolbar() {
	// 创建按钮
	ht.addHostButton = widget.NewButtonWithIcon("添加主机", theme.ContentAddIcon(), func() {
		if ht.onAddHost != nil {
			ht.onAddHost()
		}
	})
	ht.addHostButton.Importance = widget.HighImportance
	
	ht.addGroupButton = widget.NewButtonWithIcon("添加组", theme.FolderNewIcon(), func() {
		if ht.onAddGroup != nil {
			ht.onAddGroup()
		}
	})
	ht.addGroupButton.Importance = widget.MediumImportance
	
	ht.editButton = widget.NewButtonWithIcon("编辑", theme.DocumentCreateIcon(), func() {
		if ht.onEditItem != nil {
			ht.onEditItem()
		}
	})
	
	ht.deleteButton = widget.NewButtonWithIcon("删除", theme.DeleteIcon(), func() {
		if ht.onDeleteItem != nil {
			ht.onDeleteItem()
		}
	})
	ht.deleteButton.Importance = widget.DangerImportance
	
	ht.testButton = widget.NewButtonWithIcon("测试连接", theme.ConfirmIcon(), func() {
		if ht.onTestConnection != nil {
			ht.onTestConnection()
		}
	})
	ht.testButton.Importance = widget.SuccessImportance
	
	ht.importButton = widget.NewButtonWithIcon("导入", theme.FolderOpenIcon(), func() {
		if ht.onImportData != nil {
			ht.onImportData()
		}
	})
	
	ht.exportButton = widget.NewButtonWithIcon("导出", theme.DocumentSaveIcon(), func() {
		if ht.onExportData != nil {
			ht.onExportData()
		}
	})
	
	ht.refreshButton = widget.NewButtonWithIcon("刷新", theme.ViewRefreshIcon(), func() {
		if ht.onRefreshData != nil {
			ht.onRefreshData()
		}
	})
	
	// 初始状态：只有添加和刷新按钮可用
	ht.updateButtonStates(nil, nil)
	
	// 创建工具栏容器
	ht.container = container.NewHBox(
		ht.addHostButton,
		ht.addGroupButton,
		widget.NewSeparator(),
		ht.editButton,
		ht.deleteButton,
		widget.NewSeparator(),
		ht.testButton,
		widget.NewSeparator(),
		ht.importButton,
		ht.exportButton,
		widget.NewSeparator(),
		ht.refreshButton,
	)
}

// GetContainer 获取容器
func (ht *HostToolbar) GetContainer() *fyne.Container {
	return ht.container
}

// UpdateButtonStates 更新按钮状态
func (ht *HostToolbar) updateButtonStates(selectedHost *data.HostInfo, selectedGroup *data.HostGroup) {
	hasHostSelection := selectedHost != nil
	hasGroupSelection := selectedGroup != nil
	hasAnySelection := hasHostSelection || hasGroupSelection
	
	// 编辑按钮：有选择时可用
	ht.editButton.Enable()
	if !hasAnySelection {
		ht.editButton.Disable()
	}
	
	// 删除按钮：有选择时可用
	ht.deleteButton.Enable()
	if !hasAnySelection {
		ht.deleteButton.Disable()
	}
	
	// 测试连接按钮：选中主机时可用
	ht.testButton.Enable()
	if !hasHostSelection {
		ht.testButton.Disable()
	}
	
	// 导出按钮：有数据时可用（这里简化为始终可用）
	ht.exportButton.Enable()
	
	// 添加、导入和刷新按钮始终可用
	ht.addHostButton.Enable()
	ht.addGroupButton.Enable()
	ht.importButton.Enable()
	ht.refreshButton.Enable()
}

// SetSelectedItems 设置选中的项目（更新按钮状态）
func (ht *HostToolbar) SetSelectedItems(host *data.HostInfo, group *data.HostGroup) {
	ht.updateButtonStates(host, group)
}

// SetOnAddHost 设置添加主机回调
func (ht *HostToolbar) SetOnAddHost(callback func()) {
	ht.onAddHost = callback
}

// SetOnAddGroup 设置添加组回调
func (ht *HostToolbar) SetOnAddGroup(callback func()) {
	ht.onAddGroup = callback
}

// SetOnEditItem 设置编辑项目回调
func (ht *HostToolbar) SetOnEditItem(callback func()) {
	ht.onEditItem = callback
}

// SetOnDeleteItem 设置删除项目回调
func (ht *HostToolbar) SetOnDeleteItem(callback func()) {
	ht.onDeleteItem = callback
}

// SetOnTestConnection 设置测试连接回调
func (ht *HostToolbar) SetOnTestConnection(callback func()) {
	ht.onTestConnection = callback
}

// SetOnImportData 设置导入数据回调
func (ht *HostToolbar) SetOnImportData(callback func()) {
	ht.onImportData = callback
}

// SetOnExportData 设置导出数据回调
func (ht *HostToolbar) SetOnExportData(callback func()) {
	ht.onExportData = callback
}

// SetOnRefreshData 设置刷新数据回调
func (ht *HostToolbar) SetOnRefreshData(callback func()) {
	ht.onRefreshData = callback
}

// EnableAllButtons 启用所有按钮
func (ht *HostToolbar) EnableAllButtons() {
	ht.addHostButton.Enable()
	ht.addGroupButton.Enable()
	ht.editButton.Enable()
	ht.deleteButton.Enable()
	ht.testButton.Enable()
	ht.importButton.Enable()
	ht.exportButton.Enable()
	ht.refreshButton.Enable()
}

// DisableAllButtons 禁用所有按钮
func (ht *HostToolbar) DisableAllButtons() {
	ht.addHostButton.Disable()
	ht.addGroupButton.Disable()
	ht.editButton.Disable()
	ht.deleteButton.Disable()
	ht.testButton.Disable()
	ht.importButton.Disable()
	ht.exportButton.Disable()
	ht.refreshButton.Disable()
}

// SetButtonText 设置按钮文本
func (ht *HostToolbar) SetButtonText(buttonName, text string) {
	switch buttonName {
	case "addHost":
		ht.addHostButton.SetText(text)
	case "addGroup":
		ht.addGroupButton.SetText(text)
	case "edit":
		ht.editButton.SetText(text)
	case "delete":
		ht.deleteButton.SetText(text)
	case "test":
		ht.testButton.SetText(text)
	case "import":
		ht.importButton.SetText(text)
	case "export":
		ht.exportButton.SetText(text)
	case "refresh":
		ht.refreshButton.SetText(text)
	}
}

// GetButton 获取指定按钮
func (ht *HostToolbar) GetButton(buttonName string) *widget.Button {
	switch buttonName {
	case "addHost":
		return ht.addHostButton
	case "addGroup":
		return ht.addGroupButton
	case "edit":
		return ht.editButton
	case "delete":
		return ht.deleteButton
	case "test":
		return ht.testButton
	case "import":
		return ht.importButton
	case "export":
		return ht.exportButton
	case "refresh":
		return ht.refreshButton
	default:
		return nil
	}
}

// ShowProgress 显示进度状态
func (ht *HostToolbar) ShowProgress(inProgress bool) {
	if inProgress {
		ht.addHostButton.Disable()
		ht.addGroupButton.Disable()
		ht.editButton.Disable()
		ht.deleteButton.Disable()
		ht.importButton.Disable()
		ht.exportButton.Disable()
		// 测试和刷新按钮在进度中保持可用
	} else {
		// 恢复按钮状态由 updateButtonStates 控制
	}
}

// SetButtonImportance 设置按钮重要性
func (ht *HostToolbar) SetButtonImportance(buttonName string, importance widget.ButtonImportance) {
	if button := ht.GetButton(buttonName); button != nil {
		button.Importance = importance
	}
}
