package checkers

import (
	"fmt"

	"lcheck/data"
)

// SSHExecutor SSH执行器接口 - 避免循环导入
type SSHExecutor interface {
	ExecuteCommand(host data.HostInfo, command string) (string, error)
}

// Checker 检查器接口 - 定义所有检查器的通用接口
type Checker interface {
	GetName() string
	GetCategory() string
	GetChecks() []data.BaselineCheck
	RunCheck(sshClient SSHExecutor, host data.HostInfo, checkID string) (*data.BaselineCheckResult, error)
	RunAllChecks(sshClient SSHExecutor, host data.HostInfo) ([]data.BaselineCheckResult, error)
}

// Collector 信息收集器接口 - 专门用于信息收集
type Collector interface {
	GetName() string
	GetCollectors() []data.BaselineCheck
	Collect(sshClient SSHExecutor, host data.HostInfo, collectorID string) (*data.BaselineCheckResult, error)
	CollectAll(sshClient SSHExecutor, host data.HostInfo) ([]data.BaselineCheckResult, error)
}

// Manager 检查器管理器 - 统一管理所有检查器和收集器
type Manager struct {
	checkers   []Checker
	collectors []Collector
}

// NewManager 创建检查器管理器
func NewManager() *Manager {
	manager := &Manager{
		checkers:   make([]Checker, 0),
		collectors: make([]Collector, 0),
	}
	
	// 注册所有检查器
	manager.registerCheckers()
	
	// 注册所有收集器
	manager.registerCollectors()
	
	return manager
}

// registerCheckers 注册所有检查器
func (m *Manager) registerCheckers() {
	// 注册SSH检查器
	m.checkers = append(m.checkers, NewSSHChecker())
	
	// 注册密码策略检查器
	m.checkers = append(m.checkers, NewPasswordChecker())
	
	// 可以继续添加其他检查器
	// m.checkers = append(m.checkers, NewFileChecker())
	// m.checkers = append(m.checkers, NewNetworkChecker())
}

// registerCollectors 注册所有收集器
func (m *Manager) registerCollectors() {
	// 注册系统信息收集器
	m.collectors = append(m.collectors, NewSystemCollector())
	
	// 可以继续添加其他收集器
	// m.collectors = append(m.collectors, NewConfigCollector())
}

// GetAllChecks 获取所有检查项
func (m *Manager) GetAllChecks() []data.BaselineCheck {
	var allChecks []data.BaselineCheck
	
	// 收集所有检查器的检查项
	for _, checker := range m.checkers {
		checks := checker.GetChecks()
		allChecks = append(allChecks, checks...)
	}
	
	// 收集所有收集器的收集项
	for _, collector := range m.collectors {
		collectors := collector.GetCollectors()
		allChecks = append(allChecks, collectors...)
	}
	
	return allChecks
}

// RunAllChecks 运行所有检查项
func (m *Manager) RunAllChecks(sshClient SSHExecutor, host data.HostInfo) []data.BaselineCheckResult {
	var results []data.BaselineCheckResult
	
	// 运行所有收集器
	for _, collector := range m.collectors {
		collectorResults, err := collector.CollectAll(sshClient, host)
		if err != nil {
			// 记录错误但继续执行
			fmt.Printf("收集器 %s 执行失败: %v\n", collector.GetName(), err)
			continue
		}
		results = append(results, collectorResults...)
	}
	
	// 运行所有检查器
	for _, checker := range m.checkers {
		checkerResults, err := checker.RunAllChecks(sshClient, host)
		if err != nil {
			// 记录错误但继续执行
			fmt.Printf("检查器 %s 执行失败: %v\n", checker.GetName(), err)
			continue
		}
		results = append(results, checkerResults...)
	}
	
	return results
}

// RunSpecificCheck 运行特定检查项
func (m *Manager) RunSpecificCheck(sshClient SSHExecutor, host data.HostInfo, checkID string) (*data.BaselineCheckResult, error) {
	// 在收集器中查找
	for _, collector := range m.collectors {
		result, err := collector.Collect(sshClient, host, checkID)
		if err == nil {
			return result, nil
		}
	}
	
	// 在检查器中查找
	for _, checker := range m.checkers {
		result, err := checker.RunCheck(sshClient, host, checkID)
		if err == nil {
			return result, nil
		}
	}
	
	return nil, fmt.Errorf("检查项 %s 不存在", checkID)
}

// GetCheckByID 根据ID获取检查项
func (m *Manager) GetCheckByID(checkID string) *data.BaselineCheck {
	allChecks := m.GetAllChecks()
	for _, check := range allChecks {
		if check.ID == checkID {
			return &check
		}
	}
	return nil
}

// GetChecksByCategory 根据类别获取检查项
func (m *Manager) GetChecksByCategory(category string) []data.BaselineCheck {
	var categoryChecks []data.BaselineCheck
	allChecks := m.GetAllChecks()
	
	for _, check := range allChecks {
		if check.Category == category {
			categoryChecks = append(categoryChecks, check)
		}
	}
	
	return categoryChecks
}

// GetCategories 获取所有类别
func (m *Manager) GetCategories() []string {
	categoryMap := make(map[string]bool)
	allChecks := m.GetAllChecks()
	
	for _, check := range allChecks {
		categoryMap[check.Category] = true
	}
	
	var categories []string
	for category := range categoryMap {
		categories = append(categories, category)
	}
	
	return categories
}

// GetCheckCount 获取检查项总数
func (m *Manager) GetCheckCount() int {
	return len(m.GetAllChecks())
}

// GetCheckerNames 获取所有检查器名称
func (m *Manager) GetCheckerNames() []string {
	var names []string
	
	for _, checker := range m.checkers {
		names = append(names, checker.GetName())
	}
	
	for _, collector := range m.collectors {
		names = append(names, collector.GetName())
	}
	
	return names
}

// GetCheckerByName 根据名称获取检查器
func (m *Manager) GetCheckerByName(name string) Checker {
	for _, checker := range m.checkers {
		if checker.GetName() == name {
			return checker
		}
	}
	return nil
}

// GetCollectorByName 根据名称获取收集器
func (m *Manager) GetCollectorByName(name string) Collector {
	for _, collector := range m.collectors {
		if collector.GetName() == name {
			return collector
		}
	}
	return nil
}
