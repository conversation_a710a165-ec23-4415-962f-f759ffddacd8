package checkers

import (
	"fmt"
	"strings"

	"lcheck/data"
)

// PasswordChecker 密码策略检查器 - 专门负责密码相关的安全检查
type PasswordChecker struct {
	name     string
	category string
}

// NewPasswordChecker 创建密码策略检查器
func NewPasswordChecker() *PasswordChecker {
	return &PasswordChecker{
		name:     "密码策略检查器",
		category: "密码安全",
	}
}

// GetName 获取检查器名称
func (pc *PasswordChecker) GetName() string {
	return pc.name
}

// GetCategory 获取检查器类别
func (pc *PasswordChecker) GetCategory() string {
	return pc.category
}

// GetChecks 获取密码策略相关的所有检查项
func (pc *PasswordChecker) GetChecks() []data.BaselineCheck {
	return []data.BaselineCheck{
		{
			ID:          "PWD_POLICY",
			Name:        "密码策略检查",
			Category:    "密码安全",
			Description: "检查密码策略配置",
			Risk:        "中等",
			Solution:    "配置强密码策略",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   func(client interface{}, host data.HostInfo) data.BaselineCheckResult {
				return pc.checkPasswordPolicy(client.(SSHExecutor), host)
			},
		},
	}
}

// RunCheck 执行特定密码检查项
func (pc *PasswordChecker) RunCheck(sshClient SSHExecutor, host data.HostInfo, checkID string) (*data.BaselineCheckResult, error) {
	checks := pc.GetChecks()
	for _, check := range checks {
		if check.ID == checkID {
			result := check.CheckFunc(sshClient, host)
			result.CheckName = check.Name
			result.Category = check.Category
			result.Description = check.Description
			result.Risk = check.Risk
			result.Solution = check.Solution
			result.Reference = check.Reference
			return &result, nil
		}
	}
	return nil, fmt.Errorf("密码检查项 %s 不存在", checkID)
}

// RunAllChecks 执行所有密码检查项
func (pc *PasswordChecker) RunAllChecks(sshClient SSHExecutor, host data.HostInfo) ([]data.BaselineCheckResult, error) {
	var results []data.BaselineCheckResult
	checks := pc.GetChecks()
	
	for _, check := range checks {
		result := check.CheckFunc(sshClient, host)
		result.CheckName = check.Name
		result.Category = check.Category
		result.Description = check.Description
		result.Risk = check.Risk
		result.Solution = check.Solution
		result.Reference = check.Reference
		results = append(results, result)
	}
	
	return results, nil
}

// checkPasswordPolicy 检查密码策略
func (pc *PasswordChecker) checkPasswordPolicy(sshClient SSHExecutor, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		ID:      "PWD_POLICY",
		Command: "cat /etc/security/pwquality.conf 2>/dev/null | grep -v '^#' | grep -v '^$' || echo 'No policy file'",
	}

	output, err := sshClient.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output
	
	if result.Metadata == nil {
		result.Metadata = make(map[string]string)
	}

	if strings.Contains(output, "No policy file") {
		result.Status = "警告"
		result.Score = 30
		result.Details = "未找到密码策略配置文件"
		result.Metadata["policyFile"] = "不存在"
	} else {
		result.Status = "信息收集"
		result.Score = 100
		result.Details = "密码策略配置收集完成"
		result.Metadata["policyFile"] = "存在"
		
		// 解析配置项
		lines := strings.Split(output, "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line == "" {
				continue
			}
			if strings.Contains(line, "minlen") {
				parts := strings.Split(line, "=")
				if len(parts) >= 2 {
					result.Metadata["minlen"] = strings.TrimSpace(parts[1])
				}
			} else if strings.Contains(line, "minclass") {
				parts := strings.Split(line, "=")
				if len(parts) >= 2 {
					result.Metadata["minclass"] = strings.TrimSpace(parts[1])
				}
			}
		}
	}

	return result
}
