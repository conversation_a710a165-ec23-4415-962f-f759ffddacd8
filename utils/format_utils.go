package utils

import (
	"fmt"
	"strings"

	"lcheck/data"
)

// FormatUtils 格式化工具集
type FormatUtils struct{}

// NewFormatUtils 创建格式化工具集
func NewFormatUtils() *FormatUtils {
	return &FormatUtils{}
}

// FormatBytes 格式化字节数
func (fu *FormatUtils) FormatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	
	units := []string{"KB", "MB", "GB", "TB", "PB"}
	return fmt.Sprintf("%.1f %s", float64(bytes)/float64(div), units[exp])
}

// GetRiskLevel 根据分数获取风险等级
func (fu *FormatUtils) GetRiskLevel(percentage float64) string {
	if percentage >= 90 {
		return "低风险"
	} else if percentage >= 70 {
		return "中等风险"
	} else if percentage >= 50 {
		return "高风险"
	} else {
		return "严重风险"
	}
}

// GetRiskColor 获取风险等级对应的颜色
func (fu *FormatUtils) GetRiskColor(risk string) string {
	switch strings.ToLower(risk) {
	case "低风险", "low":
		return "#28a745" // 绿色
	case "中等风险", "medium":
		return "#ffc107" // 黄色
	case "高风险", "high":
		return "#fd7e14" // 橙色
	case "严重风险", "critical":
		return "#dc3545" // 红色
	default:
		return "#6c757d" // 灰色
	}
}

// GetStatusColor 获取状态对应的颜色
func (fu *FormatUtils) GetStatusColor(status string) string {
	switch strings.ToLower(status) {
	case "通过", "成功", "已完成", "success":
		return "#28a745" // 绿色
	case "警告", "warning":
		return "#ffc107" // 黄色
	case "失败", "错误", "error", "failed":
		return "#dc3545" // 红色
	case "运行中", "进行中", "running":
		return "#007bff" // 蓝色
	case "等待", "待处理", "pending":
		return "#6c757d" // 灰色
	default:
		return "#6c757d" // 默认灰色
	}
}

// FormatScore 格式化分数显示
func (fu *FormatUtils) FormatScore(score int) string {
	if score < 0 {
		return "N/A"
	}
	return fmt.Sprintf("%d", score)
}

// FormatPercentage 格式化百分比
func (fu *FormatUtils) FormatPercentage(value float64) string {
	return fmt.Sprintf("%.1f%%", value)
}

// FormatProgress 格式化进度显示
func (fu *FormatUtils) FormatProgress(current, total int) string {
	if total == 0 {
		return "0/0 (0%)"
	}
	percentage := float64(current) / float64(total) * 100
	return fmt.Sprintf("%d/%d (%.1f%%)", current, total, percentage)
}

// FormatList 格式化列表显示
func (fu *FormatUtils) FormatList(items []string, maxItems int) string {
	if len(items) == 0 {
		return "无"
	}
	
	if maxItems > 0 && len(items) > maxItems {
		displayed := items[:maxItems]
		remaining := len(items) - maxItems
		return fmt.Sprintf("%s 等%d项", strings.Join(displayed, ", "), remaining)
	}
	
	return strings.Join(items, ", ")
}

// FormatHostInfo 格式化主机信息显示
func (fu *FormatUtils) FormatHostInfo(host data.HostInfo) string {
	return fmt.Sprintf("%s (%s:%s)", host.Name, host.Host, host.Port)
}

// FormatScanResult 格式化扫描结果摘要
func (fu *FormatUtils) FormatScanResult(result data.ScanResult) string {
	return fmt.Sprintf("主机: %s, 状态: %s, 得分: %d, 检查项: %d",
		result.HostName, result.Status, result.TotalScore, len(result.CheckResults))
}

// FormatBatchScanResult 格式化批量扫描结果摘要
func (fu *FormatUtils) FormatBatchScanResult(result data.BatchScanResult) string {
	return fmt.Sprintf("任务: %s, 状态: %s, 主机数: %d, 成功: %d, 失败: %d",
		result.ID, result.Status, result.TotalHosts,
		result.CompletedHosts-result.FailedHosts,
		result.FailedHosts)
}

// FormatCheckResult 格式化检查结果显示
func (fu *FormatUtils) FormatCheckResult(result data.BaselineCheckResult) string {
	return fmt.Sprintf("[%s] %s - %s (得分: %d)", 
		result.ID, result.Status, result.Details, result.Score)
}

// FormatTableHeader 格式化表格标题
func (fu *FormatUtils) FormatTableHeader(headers []string, widths []int) string {
	var parts []string
	for i, header := range headers {
		width := 20 // 默认宽度
		if i < len(widths) {
			width = widths[i]
		}
		parts = append(parts, fu.padOrTruncate(header, width))
	}
	return strings.Join(parts, " | ")
}

// FormatTableRow 格式化表格行
func (fu *FormatUtils) FormatTableRow(values []string, widths []int) string {
	var parts []string
	for i, value := range values {
		width := 20 // 默认宽度
		if i < len(widths) {
			width = widths[i]
		}
		parts = append(parts, fu.padOrTruncate(value, width))
	}
	return strings.Join(parts, " | ")
}

// padOrTruncate 填充或截断字符串到指定宽度
func (fu *FormatUtils) padOrTruncate(s string, width int) string {
	if len(s) > width {
		if width > 3 {
			return s[:width-3] + "..."
		}
		return s[:width]
	}
	return s + strings.Repeat(" ", width-len(s))
}

// FormatKeyValue 格式化键值对显示
func (fu *FormatUtils) FormatKeyValue(key, value string) string {
	return fmt.Sprintf("%s: %s", key, value)
}

// FormatMultiline 格式化多行文本
func (fu *FormatUtils) FormatMultiline(text string, indent string) string {
	lines := strings.Split(text, "\n")
	var formattedLines []string
	
	for _, line := range lines {
		formattedLines = append(formattedLines, indent+line)
	}
	
	return strings.Join(formattedLines, "\n")
}

// FormatJSON 格式化JSON显示（简化版）
func (fu *FormatUtils) FormatJSON(jsonStr string) string {
	// 简单的JSON格式化，添加缩进
	var result strings.Builder
	var indent int
	
	for i, char := range jsonStr {
		switch char {
		case '{', '[':
			result.WriteRune(char)
			if i < len(jsonStr)-1 {
				result.WriteString("\n")
				indent++
				result.WriteString(strings.Repeat("  ", indent))
			}
		case '}', ']':
			if i > 0 && jsonStr[i-1] != '{' && jsonStr[i-1] != '[' {
				result.WriteString("\n")
				indent--
				result.WriteString(strings.Repeat("  ", indent))
			}
			result.WriteRune(char)
		case ',':
			result.WriteRune(char)
			result.WriteString("\n")
			result.WriteString(strings.Repeat("  ", indent))
		case ':':
			result.WriteString(": ")
		default:
			result.WriteRune(char)
		}
	}
	
	return result.String()
}

// FormatCommand 格式化命令显示
func (fu *FormatUtils) FormatCommand(command string) string {
	// 如果命令太长，截断显示
	maxLen := 100
	if len(command) > maxLen {
		return command[:maxLen-3] + "..."
	}
	return command
}

// FormatError 格式化错误信息显示
func (fu *FormatUtils) FormatError(err error) string {
	if err == nil {
		return ""
	}
	return fmt.Sprintf("错误: %s", err.Error())
}

// FormatTitle 格式化标题显示
func (fu *FormatUtils) FormatTitle(title string, level int) string {
	switch level {
	case 1:
		return fmt.Sprintf("# %s", title)
	case 2:
		return fmt.Sprintf("## %s", title)
	case 3:
		return fmt.Sprintf("### %s", title)
	default:
		return title
	}
}

// FormatSeparator 格式化分隔符
func (fu *FormatUtils) FormatSeparator(char string, length int) string {
	if char == "" {
		char = "-"
	}
	return strings.Repeat(char, length)
}

// 全局格式化工具实例
var FormatUtil = NewFormatUtils()
