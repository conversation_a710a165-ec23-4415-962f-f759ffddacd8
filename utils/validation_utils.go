package utils

import (
	"fmt"
	"strconv"
	"strings"

	"lcheck/data"
)

// ValidationUtils 验证工具集
type ValidationUtils struct {
	stringUtils *StringUtils
}

// NewValidationUtils 创建验证工具集
func NewValidationUtils() *ValidationUtils {
	return &ValidationUtils{
		stringUtils: NewStringUtils(),
	}
}

// ValidateHostInfo 验证主机信息
func (vu *ValidationUtils) ValidateHostInfo(host data.HostInfo) []string {
	var errors []string
	
	// 验证ID
	if strings.TrimSpace(host.ID) == "" {
		errors = append(errors, "主机ID不能为空")
	}
	
	// 验证名称
	if strings.TrimSpace(host.Name) == "" {
		errors = append(errors, "主机名不能为空")
	} else if len(host.Name) > 100 {
		errors = append(errors, "主机名长度不能超过100个字符")
	}
	
	// 验证主机地址
	if strings.TrimSpace(host.Host) == "" {
		errors = append(errors, "主机地址不能为空")
	} else {
		// 检查是否为有效IP或主机名
		if !vu.stringUtils.IsValidIP(host.Host) && !vu.stringUtils.IsValidHostname(host.Host) {
			errors = append(errors, "主机地址格式无效")
		}
	}
	
	// 验证端口
	if strings.TrimSpace(host.Port) == "" {
		errors = append(errors, "端口不能为空")
	} else {
		if port, err := strconv.Atoi(host.Port); err != nil {
			errors = append(errors, "端口必须为数字")
		} else if port < 1 || port > 65535 {
			errors = append(errors, "端口范围必须在1-65535之间")
		}
	}
	
	// 验证用户名
	if strings.TrimSpace(host.Username) == "" {
		errors = append(errors, "用户名不能为空")
	} else if len(host.Username) > 50 {
		errors = append(errors, "用户名长度不能超过50个字符")
	}
	
	// 验证密码（目前只支持密码认证）
	if strings.TrimSpace(host.Password) == "" {
		errors = append(errors, "密码不能为空")
	}
	
	// 目前只支持密码认证，不需要验证密钥路径
	
	return errors
}

// ValidateHostGroup 验证主机组信息
func (vu *ValidationUtils) ValidateHostGroup(group data.HostGroup) []string {
	var errors []string
	
	// 验证ID
	if strings.TrimSpace(group.ID) == "" {
		errors = append(errors, "主机组ID不能为空")
	}
	
	// 验证名称
	if strings.TrimSpace(group.Name) == "" {
		errors = append(errors, "主机组名不能为空")
	} else if len(group.Name) > 100 {
		errors = append(errors, "主机组名长度不能超过100个字符")
	}
	
	// 验证描述长度
	if len(group.Description) > 500 {
		errors = append(errors, "描述长度不能超过500个字符")
	}
	
	return errors
}

// ValidatePort 验证端口号
func (vu *ValidationUtils) ValidatePort(portStr string) error {
	if strings.TrimSpace(portStr) == "" {
		return fmt.Errorf("端口不能为空")
	}
	
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return fmt.Errorf("端口必须为数字")
	}
	
	if port < 1 || port > 65535 {
		return fmt.Errorf("端口范围必须在1-65535之间")
	}
	
	return nil
}

// ValidateTimeout 验证超时时间
func (vu *ValidationUtils) ValidateTimeout(timeout int) error {
	if timeout < 1 {
		return fmt.Errorf("超时时间必须大于0")
	}
	
	if timeout > 300 {
		return fmt.Errorf("超时时间不能超过300秒")
	}
	
	return nil
}

// ValidateConcurrency 验证并发数
func (vu *ValidationUtils) ValidateConcurrency(concurrency int) error {
	if concurrency < 1 {
		return fmt.Errorf("并发数必须大于0")
	}
	
	if concurrency > 100 {
		return fmt.Errorf("并发数不能超过100")
	}
	
	return nil
}

// ValidateEmail 验证邮箱地址
func (vu *ValidationUtils) ValidateEmail(email string) error {
	email = strings.TrimSpace(email)
	if email == "" {
		return fmt.Errorf("邮箱地址不能为空")
	}
	
	// 简单的邮箱格式验证
	if !strings.Contains(email, "@") {
		return fmt.Errorf("邮箱地址格式无效")
	}
	
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return fmt.Errorf("邮箱地址格式无效")
	}
	
	if len(parts[0]) == 0 || len(parts[1]) == 0 {
		return fmt.Errorf("邮箱地址格式无效")
	}
	
	if !strings.Contains(parts[1], ".") {
		return fmt.Errorf("邮箱地址格式无效")
	}
	
	return nil
}

// ValidateURL 验证URL地址
func (vu *ValidationUtils) ValidateURL(url string) error {
	url = strings.TrimSpace(url)
	if url == "" {
		return fmt.Errorf("URL不能为空")
	}
	
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		return fmt.Errorf("URL必须以http://或https://开头")
	}
	
	return nil
}

// ValidateFilePath 验证文件路径
func (vu *ValidationUtils) ValidateFilePath(path string) error {
	path = strings.TrimSpace(path)
	if path == "" {
		return fmt.Errorf("文件路径不能为空")
	}
	
	// 检查路径中的非法字符
	invalidChars := []string{"<", ">", ":", "\"", "|", "?", "*"}
	for _, char := range invalidChars {
		if strings.Contains(path, char) {
			return fmt.Errorf("文件路径包含非法字符: %s", char)
		}
	}
	
	return nil
}

// ValidatePassword 验证密码强度
func (vu *ValidationUtils) ValidatePassword(password string) []string {
	var errors []string
	
	if len(password) < 8 {
		errors = append(errors, "密码长度至少8位")
	}
	
	if len(password) > 128 {
		errors = append(errors, "密码长度不能超过128位")
	}
	
	hasUpper := false
	hasLower := false
	hasDigit := false
	hasSpecial := false
	
	for _, char := range password {
		switch {
		case char >= 'A' && char <= 'Z':
			hasUpper = true
		case char >= 'a' && char <= 'z':
			hasLower = true
		case char >= '0' && char <= '9':
			hasDigit = true
		case strings.ContainsRune("!@#$%^&*()_+-=[]{}|;:,.<>?", char):
			hasSpecial = true
		}
	}
	
	if !hasUpper {
		errors = append(errors, "密码必须包含大写字母")
	}
	
	if !hasLower {
		errors = append(errors, "密码必须包含小写字母")
	}
	
	if !hasDigit {
		errors = append(errors, "密码必须包含数字")
	}
	
	if !hasSpecial {
		errors = append(errors, "密码必须包含特殊字符")
	}
	
	return errors
}

// ValidateRange 验证数值范围
func (vu *ValidationUtils) ValidateRange(value, min, max int, fieldName string) error {
	if value < min {
		return fmt.Errorf("%s不能小于%d", fieldName, min)
	}
	
	if value > max {
		return fmt.Errorf("%s不能大于%d", fieldName, max)
	}
	
	return nil
}

// ValidateStringLength 验证字符串长度
func (vu *ValidationUtils) ValidateStringLength(s string, min, max int, fieldName string) error {
	length := len(strings.TrimSpace(s))
	
	if length < min {
		return fmt.Errorf("%s长度不能少于%d个字符", fieldName, min)
	}
	
	if length > max {
		return fmt.Errorf("%s长度不能超过%d个字符", fieldName, max)
	}
	
	return nil
}

// ValidateRequired 验证必填字段
func (vu *ValidationUtils) ValidateRequired(value, fieldName string) error {
	if strings.TrimSpace(value) == "" {
		return fmt.Errorf("%s不能为空", fieldName)
	}
	return nil
}

// ValidateChoice 验证选择项
func (vu *ValidationUtils) ValidateChoice(value string, choices []string, fieldName string) error {
	for _, choice := range choices {
		if value == choice {
			return nil
		}
	}
	
	return fmt.Errorf("%s必须是以下值之一: %s", fieldName, strings.Join(choices, ", "))
}

// IsValidJSON 检查是否为有效JSON
func (vu *ValidationUtils) IsValidJSON(jsonStr string) bool {
	jsonStr = strings.TrimSpace(jsonStr)
	if jsonStr == "" {
		return false
	}
	
	// 简单的JSON格式检查
	return (strings.HasPrefix(jsonStr, "{") && strings.HasSuffix(jsonStr, "}")) ||
		   (strings.HasPrefix(jsonStr, "[") && strings.HasSuffix(jsonStr, "]"))
}

// 全局验证工具实例
var ValidationUtil = NewValidationUtils()
