package utils

import (
	"fmt"
	"time"
)

// TimeUtils 时间工具集
type TimeUtils struct{}

// NewTimeUtils 创建时间工具集
func NewTimeUtils() *TimeUtils {
	return &TimeUtils{}
}

// FormatDuration 格式化时间间隔
func (tu *TimeUtils) FormatDuration(d time.Duration) string {
	if d < time.Second {
		return fmt.Sprintf("%dms", d.Milliseconds())
	} else if d < time.Minute {
		return fmt.Sprintf("%.1fs", d.Seconds())
	} else if d < time.Hour {
		minutes := int(d.Minutes())
		seconds := int(d.Seconds()) % 60
		return fmt.Sprintf("%dm%ds", minutes, seconds)
	} else {
		hours := int(d.Hours())
		minutes := int(d.Minutes()) % 60
		return fmt.Sprintf("%dh%dm", hours, minutes)
	}
}

// FormatTime 格式化时间
func (tu *TimeUtils) FormatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

// FormatTimeShort 格式化时间（短格式）
func (tu *TimeUtils) FormatTimeShort(t time.Time) string {
	return t.Format("01-02 15:04")
}

// FormatTimeISO 格式化时间为ISO格式
func (tu *TimeUtils) FormatTimeISO(t time.Time) string {
	return t.Format(time.RFC3339)
}

// FormatTimeCustom 自定义格式化时间
func (tu *TimeUtils) FormatTimeCustom(t time.Time, layout string) string {
	return t.Format(layout)
}

// ParseTime 解析时间字符串
func (tu *TimeUtils) ParseTime(timeStr, layout string) (time.Time, error) {
	return time.Parse(layout, timeStr)
}

// ParseTimeISO 解析ISO格式时间
func (tu *TimeUtils) ParseTimeISO(timeStr string) (time.Time, error) {
	return time.Parse(time.RFC3339, timeStr)
}

// GetTimeAgo 获取相对时间描述
func (tu *TimeUtils) GetTimeAgo(t time.Time) string {
	now := time.Now()
	diff := now.Sub(t)
	
	if diff < time.Minute {
		return "刚刚"
	} else if diff < time.Hour {
		minutes := int(diff.Minutes())
		return fmt.Sprintf("%d分钟前", minutes)
	} else if diff < 24*time.Hour {
		hours := int(diff.Hours())
		return fmt.Sprintf("%d小时前", hours)
	} else if diff < 7*24*time.Hour {
		days := int(diff.Hours() / 24)
		return fmt.Sprintf("%d天前", days)
	} else if diff < 30*24*time.Hour {
		weeks := int(diff.Hours() / (24 * 7))
		return fmt.Sprintf("%d周前", weeks)
	} else if diff < 365*24*time.Hour {
		months := int(diff.Hours() / (24 * 30))
		return fmt.Sprintf("%d个月前", months)
	} else {
		years := int(diff.Hours() / (24 * 365))
		return fmt.Sprintf("%d年前", years)
	}
}

// IsToday 检查是否为今天
func (tu *TimeUtils) IsToday(t time.Time) bool {
	now := time.Now()
	return t.Year() == now.Year() && t.YearDay() == now.YearDay()
}

// IsYesterday 检查是否为昨天
func (tu *TimeUtils) IsYesterday(t time.Time) bool {
	yesterday := time.Now().AddDate(0, 0, -1)
	return t.Year() == yesterday.Year() && t.YearDay() == yesterday.YearDay()
}

// IsThisWeek 检查是否为本周
func (tu *TimeUtils) IsThisWeek(t time.Time) bool {
	now := time.Now()
	_, thisWeek := now.ISOWeek()
	_, tWeek := t.ISOWeek()
	return now.Year() == t.Year() && thisWeek == tWeek
}

// IsThisMonth 检查是否为本月
func (tu *TimeUtils) IsThisMonth(t time.Time) bool {
	now := time.Now()
	return now.Year() == t.Year() && now.Month() == t.Month()
}

// IsThisYear 检查是否为今年
func (tu *TimeUtils) IsThisYear(t time.Time) bool {
	return time.Now().Year() == t.Year()
}

// GetStartOfDay 获取一天的开始时间
func (tu *TimeUtils) GetStartOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// GetEndOfDay 获取一天的结束时间
func (tu *TimeUtils) GetEndOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999999999, t.Location())
}

// GetStartOfWeek 获取一周的开始时间（周一）
func (tu *TimeUtils) GetStartOfWeek(t time.Time) time.Time {
	weekday := int(t.Weekday())
	if weekday == 0 {
		weekday = 7 // 将周日调整为7
	}
	return tu.GetStartOfDay(t.AddDate(0, 0, 1-weekday))
}

// GetEndOfWeek 获取一周的结束时间（周日）
func (tu *TimeUtils) GetEndOfWeek(t time.Time) time.Time {
	return tu.GetEndOfDay(tu.GetStartOfWeek(t).AddDate(0, 0, 6))
}

// GetStartOfMonth 获取一个月的开始时间
func (tu *TimeUtils) GetStartOfMonth(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
}

// GetEndOfMonth 获取一个月的结束时间
func (tu *TimeUtils) GetEndOfMonth(t time.Time) time.Time {
	return tu.GetStartOfMonth(t).AddDate(0, 1, 0).Add(-time.Nanosecond)
}

// GetStartOfYear 获取一年的开始时间
func (tu *TimeUtils) GetStartOfYear(t time.Time) time.Time {
	return time.Date(t.Year(), 1, 1, 0, 0, 0, 0, t.Location())
}

// GetEndOfYear 获取一年的结束时间
func (tu *TimeUtils) GetEndOfYear(t time.Time) time.Time {
	return time.Date(t.Year(), 12, 31, 23, 59, 59, 999999999, t.Location())
}

// AddBusinessDays 添加工作日
func (tu *TimeUtils) AddBusinessDays(t time.Time, days int) time.Time {
	result := t
	remaining := days
	
	if days > 0 {
		for remaining > 0 {
			result = result.AddDate(0, 0, 1)
			if result.Weekday() != time.Saturday && result.Weekday() != time.Sunday {
				remaining--
			}
		}
	} else if days < 0 {
		remaining = -remaining
		for remaining > 0 {
			result = result.AddDate(0, 0, -1)
			if result.Weekday() != time.Saturday && result.Weekday() != time.Sunday {
				remaining--
			}
		}
	}
	
	return result
}

// IsBusinessDay 检查是否为工作日
func (tu *TimeUtils) IsBusinessDay(t time.Time) bool {
	weekday := t.Weekday()
	return weekday != time.Saturday && weekday != time.Sunday
}

// GetBusinessDaysBetween 获取两个日期之间的工作日数量
func (tu *TimeUtils) GetBusinessDaysBetween(start, end time.Time) int {
	if start.After(end) {
		start, end = end, start
	}
	
	count := 0
	current := tu.GetStartOfDay(start)
	endDay := tu.GetStartOfDay(end)
	
	for !current.After(endDay) {
		if tu.IsBusinessDay(current) {
			count++
		}
		current = current.AddDate(0, 0, 1)
	}
	
	return count
}

// GetWeekdayName 获取星期名称
func (tu *TimeUtils) GetWeekdayName(t time.Time) string {
	weekdays := []string{"周日", "周一", "周二", "周三", "周四", "周五", "周六"}
	return weekdays[t.Weekday()]
}

// GetMonthName 获取月份名称
func (tu *TimeUtils) GetMonthName(t time.Time) string {
	months := []string{"一月", "二月", "三月", "四月", "五月", "六月",
		"七月", "八月", "九月", "十月", "十一月", "十二月"}
	return months[t.Month()-1]
}

// Sleep 休眠指定时间
func (tu *TimeUtils) Sleep(d time.Duration) {
	time.Sleep(d)
}

// Now 获取当前时间
func (tu *TimeUtils) Now() time.Time {
	return time.Now()
}

// Unix 获取Unix时间戳
func (tu *TimeUtils) Unix(t time.Time) int64 {
	return t.Unix()
}

// FromUnix 从Unix时间戳创建时间
func (tu *TimeUtils) FromUnix(timestamp int64) time.Time {
	return time.Unix(timestamp, 0)
}

// 全局时间工具实例
var TimeUtil = NewTimeUtils()
