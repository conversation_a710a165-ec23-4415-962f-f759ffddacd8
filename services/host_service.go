package services

import (
	"lcheck/core"
	"lcheck/data"
)

// HostService 主机管理服务 - 模块化门面，委托给专门的服务模块
type HostService struct {
	storage           *data.Storage
	hostManager       *HostManager
	groupManager      *GroupManager
	connectionTester  *ConnectionTester
}

// NewHostService 创建主机服务 - 使用模块化架构
func NewHostService(storage *data.Storage, sshClient *core.SSHClient) *HostService {
	// 创建服务模块
	hostManager := NewHostManager(storage)
	groupManager := NewGroupManager(storage, hostManager)
	connectionTester := NewConnectionTester(sshClient)

	return &HostService{
		storage:          storage,
		hostManager:      hostManager,
		groupManager:     groupManager,
		connectionTester: connectionTester,
	}
}

// ========== 主机管理委托方法 ==========

// LoadHosts 加载所有主机 - 委托给主机管理器
func (hs *HostService) LoadHosts() ([]data.HostInfo, error) {
	return hs.hostManager.LoadHosts()
}

// SaveHosts 保存主机列表 - 委托给主机管理器
func (hs *HostService) SaveHosts(hosts []data.HostInfo) error {
	return hs.hostManager.SaveHosts(hosts)
}

// GetHost 获取单个主机信息 - 委托给主机管理器
func (hs *HostService) GetHost(hostID string) (*data.HostInfo, error) {
	return hs.hostManager.GetHost(hostID)
}

// CreateHost 创建新主机 - 委托给主机管理器
func (hs *HostService) CreateHost(name, host, username, password string, port int) (*data.HostInfo, error) {
	return hs.hostManager.CreateHost(name, host, username, password, port)
}

// UpdateHost 更新主机信息 - 委托给主机管理器
func (hs *HostService) UpdateHost(hostID, name, host, username, password string, port int) error {
	return hs.hostManager.UpdateHost(hostID, name, host, username, password, port)
}

// DeleteHost 删除主机 - 委托给主机管理器
func (hs *HostService) DeleteHost(hostID string) error {
	return hs.hostManager.DeleteHost(hostID)
}

// DuplicateHost 复制主机 - 委托给主机管理器
func (hs *HostService) DuplicateHost(hostID string) (*data.HostInfo, error) {
	return hs.hostManager.DuplicateHost(hostID)
}

// SearchHosts 搜索主机 - 委托给主机管理器
func (hs *HostService) SearchHosts(keyword string) ([]data.HostInfo, error) {
	return hs.hostManager.SearchHosts(keyword)
}

// SortHosts 排序主机 - 委托给主机管理器
func (hs *HostService) SortHosts(hosts []data.HostInfo, sortBy string) []data.HostInfo {
	return hs.hostManager.SortHosts(hosts, sortBy)
}

// GetHostStatistics 获取主机统计信息 - 委托给主机管理器
func (hs *HostService) GetHostStatistics() (*HostStatistics, error) {
	return hs.hostManager.GetHostStatistics()
}

// ValidateHost 验证主机配置 - 委托给主机管理器
func (hs *HostService) ValidateHost(host data.HostInfo) []string {
	return hs.hostManager.ValidateHost(host)
}

// BatchCreateHosts 批量创建主机 - 委托给主机管理器
func (hs *HostService) BatchCreateHosts(hostConfigs []HostConfig) ([]data.HostInfo, []error) {
	return hs.hostManager.BatchCreateHosts(hostConfigs)
}

// BatchDeleteHosts 批量删除主机 - 委托给主机管理器
func (hs *HostService) BatchDeleteHosts(hostIDs []string) []error {
	return hs.hostManager.BatchDeleteHosts(hostIDs)
}

// ExportHosts 导出主机配置 - 委托给主机管理器
func (hs *HostService) ExportHosts(hostIDs []string) ([]data.HostInfo, error) {
	return hs.hostManager.ExportHosts(hostIDs)
}

// ImportHosts 导入主机配置 - 委托给主机管理器
func (hs *HostService) ImportHosts(hosts []data.HostInfo) (int, []error) {
	return hs.hostManager.ImportHosts(hosts)
}

// ========== 主机组管理委托方法 ==========

// LoadGroups 加载所有主机组 - 委托给主机组管理器
func (hs *HostService) LoadGroups() ([]data.HostGroup, error) {
	return hs.groupManager.LoadGroups()
}

// SaveGroups 保存主机组列表 - 委托给主机组管理器
func (hs *HostService) SaveGroups(groups []data.HostGroup) error {
	return hs.groupManager.SaveGroups(groups)
}

// GetGroup 获取单个主机组信息 - 委托给主机组管理器
func (hs *HostService) GetGroup(groupID string) (*data.HostGroup, error) {
	return hs.groupManager.GetGroup(groupID)
}

// CreateGroup 创建新主机组 - 委托给主机组管理器
func (hs *HostService) CreateGroup(name, description string, hostIDs []string) (*data.HostGroup, error) {
	return hs.groupManager.CreateGroup(name, description, hostIDs)
}

// UpdateGroup 更新主机组 - 委托给主机组管理器
func (hs *HostService) UpdateGroup(groupID, name, description string, hostIDs []string) error {
	return hs.groupManager.UpdateGroup(groupID, name, description, hostIDs)
}

// DeleteGroup 删除主机组 - 委托给主机组管理器
func (hs *HostService) DeleteGroup(groupID string) error {
	return hs.groupManager.DeleteGroup(groupID)
}

// GetGroupHosts 获取主机组中的所有主机 - 委托给主机组管理器
func (hs *HostService) GetGroupHosts(groupID string) ([]data.HostInfo, error) {
	return hs.groupManager.GetGroupHosts(groupID)
}

// AddHostsToGroup 向主机组添加主机 - 委托给主机组管理器
func (hs *HostService) AddHostsToGroup(groupID string, hostIDs []string) error {
	return hs.groupManager.AddHostsToGroup(groupID, hostIDs)
}

// RemoveHostsFromGroup 从主机组移除主机 - 委托给主机组管理器
func (hs *HostService) RemoveHostsFromGroup(groupID string, hostIDs []string) error {
	return hs.groupManager.RemoveHostsFromGroup(groupID, hostIDs)
}

// DuplicateGroup 复制主机组 - 委托给主机组管理器
func (hs *HostService) DuplicateGroup(groupID string) (*data.HostGroup, error) {
	return hs.groupManager.DuplicateGroup(groupID)
}

// SearchGroups 搜索主机组 - 委托给主机组管理器
func (hs *HostService) SearchGroups(keyword string) ([]data.HostGroup, error) {
	return hs.groupManager.SearchGroups(keyword)
}

// SortGroups 排序主机组 - 委托给主机组管理器
func (hs *HostService) SortGroups(groups []data.HostGroup, sortBy string) []data.HostGroup {
	return hs.groupManager.SortGroups(groups, sortBy)
}

// GetGroupStatistics 获取主机组统计信息 - 委托给主机组管理器
func (hs *HostService) GetGroupStatistics() (*GroupStatistics, error) {
	return hs.groupManager.GetGroupStatistics()
}

// ValidateGroup 验证主机组配置 - 委托给主机组管理器
func (hs *HostService) ValidateGroup(group data.HostGroup) []string {
	return hs.groupManager.ValidateGroup(group)
}

// BatchDeleteGroups 批量删除主机组 - 委托给主机组管理器
func (hs *HostService) BatchDeleteGroups(groupIDs []string) []error {
	return hs.groupManager.BatchDeleteGroups(groupIDs)
}

// ExportGroups 导出主机组配置 - 委托给主机组管理器
func (hs *HostService) ExportGroups(groupIDs []string) ([]data.HostGroup, error) {
	return hs.groupManager.ExportGroups(groupIDs)
}

// ImportGroups 导入主机组配置 - 委托给主机组管理器
func (hs *HostService) ImportGroups(groups []data.HostGroup) (int, []error) {
	return hs.groupManager.ImportGroups(groups)
}

// ========== 连接测试委托方法 ==========

// TestConnection 测试主机连接 - 委托给连接测试器
func (hs *HostService) TestConnection(host data.HostInfo) *ConnectionTestResult {
	return hs.connectionTester.TestConnection(host)
}

// TestMultipleConnections 测试多个主机连接 - 委托给连接测试器
func (hs *HostService) TestMultipleConnections(hosts []data.HostInfo, concurrency int) []*ConnectionTestResult {
	return hs.connectionTester.TestMultipleConnections(hosts, concurrency)
}

// TestGroupConnections 测试主机组连接 - 委托给连接测试器
func (hs *HostService) TestGroupConnections(group data.HostGroup, hosts []data.HostInfo, concurrency int) *GroupTestResult {
	return hs.connectionTester.TestGroupConnections(group, hosts, concurrency)
}

// TestConnectionWithDetails 测试连接并获取详细信息 - 委托给连接测试器
func (hs *HostService) TestConnectionWithDetails(host data.HostInfo) *DetailedConnectionTestResult {
	return hs.connectionTester.TestConnectionWithDetails(host)
}

// GetConnectionStatistics 获取连接统计信息 - 委托给连接测试器
func (hs *HostService) GetConnectionStatistics(results []*ConnectionTestResult) *ConnectionStatistics {
	return hs.connectionTester.GetConnectionStatistics(results)
}

// ========== 服务管理器访问方法 ==========

// GetHostManager 获取主机管理器
func (hs *HostService) GetHostManager() *HostManager {
	return hs.hostManager
}

// GetGroupManager 获取主机组管理器
func (hs *HostService) GetGroupManager() *GroupManager {
	return hs.groupManager
}

// GetConnectionTester 获取连接测试器
func (hs *HostService) GetConnectionTester() *ConnectionTester {
	return hs.connectionTester
}
