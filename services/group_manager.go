package services

import (
	"fmt"
	"strings"

	"lcheck/data"
	"lcheck/utils"
)

// GroupManager 主机组管理器 - 专门负责主机组的CRUD操作
type GroupManager struct {
	storage     *data.Storage
	hostManager *HostManager
}

// NewGroupManager 创建主机组管理器
func NewGroupManager(storage *data.Storage, hostManager *HostManager) *GroupManager {
	return &GroupManager{
		storage:     storage,
		hostManager: hostManager,
	}
}

// LoadGroups 加载所有主机组
func (gm *GroupManager) LoadGroups() ([]data.HostGroup, error) {
	return gm.storage.LoadGroups()
}

// SaveGroups 保存主机组列表
func (gm *GroupManager) SaveGroups(groups []data.HostGroup) error {
	return gm.storage.SaveGroups(groups)
}

// GetGroup 获取单个主机组信息
func (gm *GroupManager) GetGroup(groupID string) (*data.HostGroup, error) {
	return gm.storage.GetGroup(groupID)
}

// CreateGroup 创建新主机组
func (gm *GroupManager) CreateGroup(name, description string, hostIDs []string) (*data.HostGroup, error) {
	// 验证输入
	if err := gm.validateGroupInput(name, hostIDs); err != nil {
		return nil, err
	}

	// 验证主机ID是否存在
	if err := gm.validateHostIDs(hostIDs); err != nil {
		return nil, err
	}

	// 转换hostIDs为HostInfo列表
	var hosts []data.HostInfo
	for _, hostID := range hostIDs {
		if host, err := gm.storage.GetHost(hostID); err == nil {
			hosts = append(hosts, *host)
		}
	}

	// 创建主机组
	group := &data.HostGroup{
		ID:          utils.GenerateID("group"),
		Name:        strings.TrimSpace(name),
		Description: strings.TrimSpace(description),
		Hosts:       hosts,
	}

	// 保存主机组
	if err := gm.storage.SaveGroup(*group); err != nil {
		return nil, fmt.Errorf("保存主机组失败: %v", err)
	}

	return group, nil
}

// UpdateGroup 更新主机组
func (gm *GroupManager) UpdateGroup(groupID, name, description string, hostIDs []string) error {
	// 验证输入
	if err := gm.validateGroupInput(name, hostIDs); err != nil {
		return err
	}

	// 验证主机ID是否存在
	if err := gm.validateHostIDs(hostIDs); err != nil {
		return err
	}

	// 获取现有主机组信息
	existingGroup, err := gm.storage.GetGroup(groupID)
	if err != nil {
		return fmt.Errorf("获取主机组信息失败: %v", err)
	}

	// 转换hostIDs为HostInfo列表
	var hosts []data.HostInfo
	for _, hostID := range hostIDs {
		if host, err := gm.storage.GetHost(hostID); err == nil {
			hosts = append(hosts, *host)
		}
	}

	// 更新主机组信息
	existingGroup.Name = strings.TrimSpace(name)
	existingGroup.Description = strings.TrimSpace(description)
	existingGroup.Hosts = hosts

	// 保存更新后的主机组信息
	return gm.storage.SaveGroup(*existingGroup)
}

// DeleteGroup 删除主机组
func (gm *GroupManager) DeleteGroup(groupID string) error {
	return gm.storage.DeleteGroup(groupID)
}

// GetGroupHosts 获取主机组中的所有主机
func (gm *GroupManager) GetGroupHosts(groupID string) ([]data.HostInfo, error) {
	group, err := gm.storage.GetGroup(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取主机组失败: %v", err)
	}

	var hosts []data.HostInfo
	for _, groupHost := range group.Hosts {
		host, err := gm.storage.GetHost(groupHost.ID)
		if err != nil {
			// 跳过不存在的主机
			continue
		}
		hosts = append(hosts, *host)
	}

	return hosts, nil
}

// AddHostsToGroup 向主机组添加主机
func (gm *GroupManager) AddHostsToGroup(groupID string, hostIDs []string) error {
	// 验证主机ID是否存在
	if err := gm.validateHostIDs(hostIDs); err != nil {
		return err
	}

	// 获取主机组
	group, err := gm.storage.GetGroup(groupID)
	if err != nil {
		return fmt.Errorf("获取主机组失败: %v", err)
	}

	// 添加主机ID（去重）
	existingHosts := make(map[string]bool)
	for _, groupHost := range group.Hosts {
		existingHosts[groupHost.ID] = true
	}

	for _, hostID := range hostIDs {
		if !existingHosts[hostID] {
			if host, err := gm.storage.GetHost(hostID); err == nil {
				group.Hosts = append(group.Hosts, *host)
			}
		}
	}

	// 保存更新后的主机组
	return gm.storage.SaveGroup(*group)
}

// RemoveHostsFromGroup 从主机组移除主机
func (gm *GroupManager) RemoveHostsFromGroup(groupID string, hostIDs []string) error {
	// 获取主机组
	group, err := gm.storage.GetGroup(groupID)
	if err != nil {
		return fmt.Errorf("获取主机组失败: %v", err)
	}

	// 创建要移除的主机ID映射
	removeHosts := make(map[string]bool)
	for _, hostID := range hostIDs {
		removeHosts[hostID] = true
	}

	// 过滤掉要移除的主机
	var newHosts []data.HostInfo
	for _, groupHost := range group.Hosts {
		if !removeHosts[groupHost.ID] {
			newHosts = append(newHosts, groupHost)
		}
	}

	group.Hosts = newHosts

	// 保存更新后的主机组
	return gm.storage.SaveGroup(*group)
}

// DuplicateGroup 复制主机组
func (gm *GroupManager) DuplicateGroup(groupID string) (*data.HostGroup, error) {
	// 获取原主机组信息
	originalGroup, err := gm.storage.GetGroup(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取原主机组信息失败: %v", err)
	}

	// 创建副本
	duplicatedGroup := *originalGroup
	duplicatedGroup.ID = utils.GenerateID("group")
	duplicatedGroup.Name = originalGroup.Name + "_副本"

	// 保存副本
	if err := gm.storage.SaveGroup(duplicatedGroup); err != nil {
		return nil, fmt.Errorf("保存主机组副本失败: %v", err)
	}

	return &duplicatedGroup, nil
}

// SearchGroups 搜索主机组
func (gm *GroupManager) SearchGroups(keyword string) ([]data.HostGroup, error) {
	groups, err := gm.LoadGroups()
	if err != nil {
		return nil, err
	}

	return utils.FilterGroups(groups, keyword), nil
}

// SortGroups 排序主机组
func (gm *GroupManager) SortGroups(groups []data.HostGroup, sortBy string) []data.HostGroup {
	switch sortBy {
	case "name":
		return utils.SortGroupsByName(groups)
	default:
		return groups
	}
}

// GetGroupStatistics 获取主机组统计信息
func (gm *GroupManager) GetGroupStatistics() (*GroupStatistics, error) {
	groups, err := gm.LoadGroups()
	if err != nil {
		return nil, err
	}

	stats := &GroupStatistics{
		TotalGroups:   len(groups),
		EmptyGroups:   0,
		LargestGroup:  0,
		SmallestGroup: 0,
		AverageSize:   0,
	}

	if len(groups) == 0 {
		return stats, nil
	}

	totalHosts := 0
	stats.SmallestGroup = len(groups[0].Hosts)

	for _, group := range groups {
		hostCount := len(group.Hosts)
		totalHosts += hostCount

		if hostCount == 0 {
			stats.EmptyGroups++
		}

		if hostCount > stats.LargestGroup {
			stats.LargestGroup = hostCount
		}

		if hostCount < stats.SmallestGroup {
			stats.SmallestGroup = hostCount
		}
	}

	stats.AverageSize = float64(totalHosts) / float64(len(groups))

	return stats, nil
}

// ValidateGroup 验证主机组配置
func (gm *GroupManager) ValidateGroup(group data.HostGroup) []string {
	return utils.ValidateHostGroup(group)
}

// BatchDeleteGroups 批量删除主机组
func (gm *GroupManager) BatchDeleteGroups(groupIDs []string) []error {
	var errors []error

	for _, groupID := range groupIDs {
		if err := gm.DeleteGroup(groupID); err != nil {
			errors = append(errors, fmt.Errorf("删除主机组 %s 失败: %v", groupID, err))
		}
	}

	return errors
}

// ExportGroups 导出主机组配置
func (gm *GroupManager) ExportGroups(groupIDs []string) ([]data.HostGroup, error) {
	var exportGroups []data.HostGroup

	for _, groupID := range groupIDs {
		group, err := gm.storage.GetGroup(groupID)
		if err != nil {
			continue
		}
		exportGroups = append(exportGroups, *group)
	}

	return exportGroups, nil
}

// ImportGroups 导入主机组配置
func (gm *GroupManager) ImportGroups(groups []data.HostGroup) (int, []error) {
	var errors []error
	successCount := 0

	for _, group := range groups {
		// 生成新的ID
		group.ID = utils.GenerateID("group")
		
		// 验证主机组信息
		if validationErrors := gm.ValidateGroup(group); len(validationErrors) > 0 {
			errors = append(errors, fmt.Errorf("主机组 %s 验证失败: %v", group.Name, validationErrors))
			continue
		}

		// 验证主机ID是否存在
		if err := gm.validateGroupHosts(group.Hosts); err != nil {
			errors = append(errors, fmt.Errorf("主机组 %s 包含无效主机: %v", group.Name, err))
			continue
		}

		// 保存主机组
		if err := gm.storage.SaveGroup(group); err != nil {
			errors = append(errors, fmt.Errorf("导入主机组 %s 失败: %v", group.Name, err))
			continue
		}

		successCount++
	}

	return successCount, errors
}

// validateGroupInput 验证主机组输入
func (gm *GroupManager) validateGroupInput(name string, hostIDs []string) error {
	if strings.TrimSpace(name) == "" {
		return fmt.Errorf("主机组名不能为空")
	}

	if len(hostIDs) == 0 {
		return fmt.Errorf("主机组必须包含至少一个主机")
	}

	return nil
}

// validateHostIDs 验证主机ID列表
func (gm *GroupManager) validateHostIDs(hostIDs []string) error {
	for _, hostID := range hostIDs {
		_, err := gm.storage.GetHost(hostID)
		if err != nil {
			return fmt.Errorf("主机 %s 不存在", hostID)
		}
	}
	return nil
}

// validateGroupHosts 验证主机组中的主机列表
func (gm *GroupManager) validateGroupHosts(hosts []data.HostInfo) error {
	for _, host := range hosts {
		_, err := gm.storage.GetHost(host.ID)
		if err != nil {
			return fmt.Errorf("主机 %s 不存在", host.ID)
		}
	}
	return nil
}

// GroupStatistics 主机组统计信息
type GroupStatistics struct {
	TotalGroups   int     `json:"totalGroups"`
	EmptyGroups   int     `json:"emptyGroups"`
	LargestGroup  int     `json:"largestGroup"`
	SmallestGroup int     `json:"smallestGroup"`
	AverageSize   float64 `json:"averageSize"`
}
