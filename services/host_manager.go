package services

import (
	"fmt"
	"strings"

	"lcheck/data"
	"lcheck/utils"
)

// HostManager 主机管理器 - 专门负责主机的CRUD操作
type HostManager struct {
	storage *data.Storage
}

// NewHostManager 创建主机管理器
func NewHostManager(storage *data.Storage) *HostManager {
	return &HostManager{
		storage: storage,
	}
}

// LoadHosts 加载所有主机
func (hm *HostManager) LoadHosts() ([]data.HostInfo, error) {
	return hm.storage.LoadHosts()
}

// SaveHosts 保存主机列表
func (hm *HostManager) SaveHosts(hosts []data.HostInfo) error {
	return hm.storage.SaveHosts(hosts)
}

// GetHost 获取单个主机信息
func (hm *HostManager) GetHost(hostID string) (*data.HostInfo, error) {
	return hm.storage.GetHost(hostID)
}

// CreateHost 创建新主机
func (hm *HostManager) CreateHost(name, host, username, password string, port int) (*data.HostInfo, error) {
	// 验证输入
	if err := hm.validateHostInput(name, host, username, password, port); err != nil {
		return nil, err
	}

	// 创建主机信息
	hostInfo := &data.HostInfo{
		ID:          utils.GenerateID("host"),
		Name:        strings.TrimSpace(name),
		Host:        strings.TrimSpace(host),
		Port:        fmt.Sprintf("%d", port),
		Username:    strings.TrimSpace(username),
		Password:    password,
		Description: "",
	}

	// 保存主机
	if err := hm.storage.SaveHost(*hostInfo); err != nil {
		return nil, fmt.Errorf("保存主机失败: %v", err)
	}

	return hostInfo, nil
}

// UpdateHost 更新主机信息
func (hm *HostManager) UpdateHost(hostID, name, host, username, password string, port int) error {
	// 验证输入
	if err := hm.validateHostInput(name, host, username, password, port); err != nil {
		return err
	}

	// 获取现有主机信息
	existingHost, err := hm.storage.GetHost(hostID)
	if err != nil {
		return fmt.Errorf("获取主机信息失败: %v", err)
	}

	// 更新主机信息
	existingHost.Name = strings.TrimSpace(name)
	existingHost.Host = strings.TrimSpace(host)
	existingHost.Port = fmt.Sprintf("%d", port)
	existingHost.Username = strings.TrimSpace(username)
	if password != "" {
		existingHost.Password = password
	}

	// 保存更新后的主机信息
	return hm.storage.SaveHost(*existingHost)
}

// DeleteHost 删除主机
func (hm *HostManager) DeleteHost(hostID string) error {
	return hm.storage.DeleteHost(hostID)
}

// DuplicateHost 复制主机
func (hm *HostManager) DuplicateHost(hostID string) (*data.HostInfo, error) {
	// 获取原主机信息
	originalHost, err := hm.storage.GetHost(hostID)
	if err != nil {
		return nil, fmt.Errorf("获取原主机信息失败: %v", err)
	}

	// 创建副本
	duplicatedHost := *originalHost
	duplicatedHost.ID = utils.GenerateID("host")
	duplicatedHost.Name = originalHost.Name + "_副本"

	// 保存副本
	if err := hm.storage.SaveHost(duplicatedHost); err != nil {
		return nil, fmt.Errorf("保存主机副本失败: %v", err)
	}

	return &duplicatedHost, nil
}

// SearchHosts 搜索主机
func (hm *HostManager) SearchHosts(keyword string) ([]data.HostInfo, error) {
	hosts, err := hm.LoadHosts()
	if err != nil {
		return nil, err
	}

	return utils.FilterHosts(hosts, keyword), nil
}

// SortHosts 排序主机
func (hm *HostManager) SortHosts(hosts []data.HostInfo, sortBy string) []data.HostInfo {
	switch sortBy {
	case "name":
		return utils.SortHostsByName(hosts)
	default:
		return hosts
	}
}

// GetHostStatistics 获取主机统计信息
func (hm *HostManager) GetHostStatistics() (*HostStatistics, error) {
	hosts, err := hm.LoadHosts()
	if err != nil {
		return nil, err
	}

	stats := &HostStatistics{
		TotalHosts:    len(hosts),
		ActiveHosts:   0,
		InactiveHosts: 0,
		AuthTypes:     make(map[string]int),
	}

	for range hosts {
		stats.AuthTypes["password"]++ // 目前只支持密码认证
		// 这里可以添加活跃状态检查逻辑
		stats.ActiveHosts++
	}

	return stats, nil
}

// ValidateHost 验证主机配置
func (hm *HostManager) ValidateHost(host data.HostInfo) []string {
	return utils.ValidateHostInfo(host)
}

// TestConnection 测试主机连接
func (hm *HostManager) TestConnection(host data.HostInfo) error {
	// 这里应该实现实际的连接测试逻辑
	// 目前返回成功，实际应该使用SSH客户端测试
	return nil
}

// BatchCreateHosts 批量创建主机
func (hm *HostManager) BatchCreateHosts(hostConfigs []HostConfig) ([]data.HostInfo, []error) {
	var createdHosts []data.HostInfo
	var errors []error

	for _, config := range hostConfigs {
		host, err := hm.CreateHost(config.Name, config.Host, config.Username, config.Password, config.Port)
		if err != nil {
			errors = append(errors, fmt.Errorf("创建主机 %s 失败: %v", config.Name, err))
			continue
		}
		createdHosts = append(createdHosts, *host)
	}

	return createdHosts, errors
}

// BatchDeleteHosts 批量删除主机
func (hm *HostManager) BatchDeleteHosts(hostIDs []string) []error {
	var errors []error

	for _, hostID := range hostIDs {
		if err := hm.DeleteHost(hostID); err != nil {
			errors = append(errors, fmt.Errorf("删除主机 %s 失败: %v", hostID, err))
		}
	}

	return errors
}

// ExportHosts 导出主机配置
func (hm *HostManager) ExportHosts(hostIDs []string) ([]data.HostInfo, error) {
	var exportHosts []data.HostInfo

	for _, hostID := range hostIDs {
		host, err := hm.storage.GetHost(hostID)
		if err != nil {
			continue
		}
		
		// 清除敏感信息
		exportHost := *host
		exportHost.Password = ""
		
		exportHosts = append(exportHosts, exportHost)
	}

	return exportHosts, nil
}

// ImportHosts 导入主机配置
func (hm *HostManager) ImportHosts(hosts []data.HostInfo) (int, []error) {
	var errors []error
	successCount := 0

	for _, host := range hosts {
		// 生成新的ID
		host.ID = utils.GenerateID("host")
		
		// 验证主机信息
		if validationErrors := hm.ValidateHost(host); len(validationErrors) > 0 {
			errors = append(errors, fmt.Errorf("主机 %s 验证失败: %v", host.Name, validationErrors))
			continue
		}

		// 保存主机
		if err := hm.storage.SaveHost(host); err != nil {
			errors = append(errors, fmt.Errorf("导入主机 %s 失败: %v", host.Name, err))
			continue
		}

		successCount++
	}

	return successCount, errors
}

// validateHostInput 验证主机输入
func (hm *HostManager) validateHostInput(name, host, username, password string, port int) error {
	if strings.TrimSpace(name) == "" {
		return fmt.Errorf("主机名不能为空")
	}

	if strings.TrimSpace(host) == "" {
		return fmt.Errorf("主机地址不能为空")
	}

	if !utils.IsValidIP(host) && !utils.IsValidHostname(host) {
		return fmt.Errorf("主机地址格式无效")
	}

	if port < 1 || port > 65535 {
		return fmt.Errorf("端口必须在1-65535之间")
	}

	if strings.TrimSpace(username) == "" {
		return fmt.Errorf("用户名不能为空")
	}

	if strings.TrimSpace(password) == "" {
		return fmt.Errorf("密码不能为空")
	}

	return nil
}

// HostConfig 主机配置结构
type HostConfig struct {
	Name     string
	Host     string
	Username string
	Password string
	Port     int
}

// HostStatistics 主机统计信息
type HostStatistics struct {
	TotalHosts    int            `json:"totalHosts"`
	ActiveHosts   int            `json:"activeHosts"`
	InactiveHosts int            `json:"inactiveHosts"`
	AuthTypes     map[string]int `json:"authTypes"`
}
