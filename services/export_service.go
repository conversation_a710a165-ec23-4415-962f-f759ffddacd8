package services

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"lcheck/data"
)

// ExportOptions 导出选项
type ExportOptions struct {
	Format      string // "html", "csv", "json"
	Content     string // "全部内容", "仅失败项目", "仅摘要信息"
	IncludeRaw  bool   // 是否包含原始输出
}

// ExportService 导出服务
type ExportService struct {
	storage *data.Storage
}

// NewExportService 创建导出服务
func NewExportService(storage *data.Storage) *ExportService {
	return &ExportService{
		storage: storage,
	}
}

// ExportTask 导出任务结果
func (es *ExportService) ExportTask(task *data.ScanTask, options ExportOptions) (string, error) {
	switch options.Format {
	case "html":
		return es.exportToHTML(task, options)
	case "csv":
		return es.exportToCSV(task, options)
	case "json":
		return es.exportToJSON(task, options)
	default:
		return "", fmt.Errorf("不支持的导出格式: %s", options.Format)
	}
}

// SaveExportFile 保存导出文件
func (es *ExportService) SaveExportFile(filePath, content string) error {
	// 确保exports目录存在
	exportDir := filepath.Dir(filePath)
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(filePath, []byte(content), 0644); err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	return nil
}

// GenerateFileName 生成导出文件名
func (es *ExportService) GenerateFileName(task *data.ScanTask, format string) string {
	timestamp := time.Now().Format("20060102_150405")
	taskName := strings.ReplaceAll(task.Name, " ", "_")
	return fmt.Sprintf("%s_%s.%s", taskName, timestamp, format)
}

// FormatFileSize 格式化文件大小
func (es *ExportService) FormatFileSize(size int) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// findCheckResult 查找特定ID的检查结果
func (es *ExportService) findCheckResult(checkResults []data.BaselineCheckResult, checkID string) *data.BaselineCheckResult {
	for i := range checkResults {
		if checkResults[i].ID == checkID {
			return &checkResults[i]
		}
	}
	return nil
}

// escapeCSV 转义CSV字段中的特殊字符
func (es *ExportService) escapeCSV(field string) string {
	if strings.Contains(field, ",") || strings.Contains(field, "\"") || strings.Contains(field, "\n") {
		field = strings.ReplaceAll(field, "\"", "\"\"")
		field = "\"" + field + "\""
	}
	return field
}

// exportToHTML 导出为HTML格式
func (es *ExportService) exportToHTML(task *data.ScanTask, options ExportOptions) (string, error) {
	htmlService := NewHTMLTemplateService()
	securityService := NewSecurityTableService()

	var buffer strings.Builder

	// HTML头部
	buffer.WriteString(htmlService.GenerateHTMLHeader())

	// 任务摘要
	buffer.WriteString(htmlService.GenerateTaskSummary(task))

	// 主机结果
	for i, result := range task.Results {
		// 主机头部
		buffer.WriteString(htmlService.GenerateHostHeader(i, &result))

		// 得分信息
		buffer.WriteString(htmlService.GenerateScoreInfo(&result))

		// 安全检查表格 - 按模板格式：实际结果 + 建议配置
		buffer.WriteString(securityService.GeneratePasswordPolicyTable(result.CheckResults))
		buffer.WriteString(securityService.GenerateSSHConfigTable(result.CheckResults))
		buffer.WriteString(securityService.GeneratePortStatusTable(result.CheckResults))

		// 检查结果表格
		buffer.WriteString(htmlService.GenerateCheckResultsTable(result.CheckResults, options.Content))

		buffer.WriteString(`
            </div>
        </div>
`)
	}

	// HTML尾部
	buffer.WriteString(htmlService.GenerateHTMLFooter())

	return buffer.String(), nil
}

// exportToCSV 导出为CSV格式
func (es *ExportService) exportToCSV(task *data.ScanTask, options ExportOptions) (string, error) {
	var buffer strings.Builder

	// CSV头部
	buffer.WriteString("主机名,主机地址,检查项,类别,结果,风险等级,得分,详情,解决方案\n")

	// 数据行
	for _, result := range task.Results {
		for _, check := range result.CheckResults {
			if options.Content == "仅失败项目" && check.Status == "通过" {
				continue
			}

			buffer.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s,%s,%d,%s,%s\n",
				es.escapeCSV(result.HostName),
				es.escapeCSV(result.Host),
				es.escapeCSV(check.CheckName),
				es.escapeCSV(check.Category),
				es.escapeCSV(check.Status),
				es.escapeCSV(check.Risk),
				check.Score,
				es.escapeCSV(check.Details),
				es.escapeCSV(check.Solution)))
		}
	}

	return buffer.String(), nil
}

// exportToJSON 导出为JSON格式
func (es *ExportService) exportToJSON(task *data.ScanTask, options ExportOptions) (string, error) {
	// 这里可以实现JSON导出逻辑
	return fmt.Sprintf(`{"task": "%s", "status": "not implemented"}`, task.Name), nil
}
