package services

import (
	"fmt"
	"strings"
	"time"

	"lcheck/data"
)

// HTMLTemplateService HTML模板服务
type HTMLTemplateService struct{}

// NewHTMLTemplateService 创建HTML模板服务
func NewHTMLTemplateService() *HTMLTemplateService {
	return &HTMLTemplateService{}
}

// GenerateHTMLHeader 生成HTML头部
func (hts *HTMLTemplateService) GenerateHTMLHeader() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lcheck 安全扫描报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 2px solid #007acc; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #007acc; font-size: 28px; margin-bottom: 10px; }
        .subtitle { color: #666; font-size: 16px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007acc; }
        .summary-title { font-weight: bold; color: #333; margin-bottom: 5px; }
        .summary-value { font-size: 24px; font-weight: bold; color: #007acc; }
        .host-section { margin-bottom: 30px; border: 1px solid #ddd; border-radius: 6px; overflow: hidden; }
        .host-header { background: #007acc; color: white; padding: 15px; font-weight: bold; }
        .host-content { padding: 20px; }
        .check-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .check-table th, .check-table td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        .check-table th { background: #f8f9fa; font-weight: bold; }
        .status-pass { color: #28a745; font-weight: bold; }
        .status-fail { color: #dc3545; font-weight: bold; }
        .status-warn { color: #ffc107; font-weight: bold; }
        .risk-high { background: #ffebee; color: #c62828; }
        .risk-medium { background: #fff3e0; color: #ef6c00; }
        .risk-low { background: #e8f5e8; color: #2e7d32; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        table th, table td { padding: 8px 12px; text-align: left; border: 1px solid #ddd; }
        table th { background-color: #f8f9fa; font-weight: bold; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        code { font-family: 'Courier New', monospace; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🛡️ Lcheck 安全扫描报告</div>
            <div class="subtitle">Linux远程基线安全检查报告</div>
        </div>`
}

// GenerateTaskSummary 生成任务摘要
func (hts *HTMLTemplateService) GenerateTaskSummary(task *data.ScanTask) string {
	return fmt.Sprintf(`
        <div class="summary">
            <div class="summary-card">
                <div class="summary-title">📋 任务名称</div>
                <div class="summary-value">%s</div>
            </div>
            <div class="summary-card">
                <div class="summary-title">📊 任务状态</div>
                <div class="summary-value">%s</div>
            </div>
            <div class="summary-card">
                <div class="summary-title">🖥️ 扫描主机</div>
                <div class="summary-value">%d 台</div>
            </div>
            <div class="summary-card">
                <div class="summary-title">⏰ 完成时间</div>
                <div class="summary-value">%s</div>
            </div>
        </div>`, 
		task.Name, 
		task.Status, 
		len(task.Results),
		func() string {
			if task.CompletedAt != nil {
				return task.CompletedAt.Format("2006-01-02 15:04:05")
			}
			return "未完成"
		}())
}

// GenerateHostHeader 生成主机头部
func (hts *HTMLTemplateService) GenerateHostHeader(index int, result *data.ScanResult) string {
	return fmt.Sprintf(`
        <div class="host-section">
            <div class="host-header">
                🖥️ 主机 %d: %s (%s)
            </div>
            <div class="host-content">
                <p><strong>扫描状态:</strong> %s</p>
                <p><strong>检查统计:</strong> 总计 %d 项，通过 %d 项，失败 %d 项，警告 %d 项</p>`,
		index+1, result.HostName, result.Host, result.Status,
		result.TotalChecks, result.PassedChecks, result.FailedChecks, result.WarningChecks)
}

// GenerateScoreInfo 生成得分信息
func (hts *HTMLTemplateService) GenerateScoreInfo(result *data.ScanResult) string {
	if result.TotalScore > 0 && result.MaxScore > 0 {
		scorePercentage := float64(result.TotalScore) / float64(result.MaxScore) * 100
		return fmt.Sprintf(`                <p><strong>安全得分:</strong> %d/%d (%.1f%%)</p>
`, result.TotalScore, result.MaxScore, scorePercentage)
	}
	return ""
}

// GenerateHTMLFooter 生成HTML尾部
func (hts *HTMLTemplateService) GenerateHTMLFooter() string {
	return fmt.Sprintf(`
        <div class="footer">
            <p>报告生成时间: %s | 生成工具: Lcheck v3.2.0</p>
            <p>Linux远程基线安全检查工具 - 专业安全检查解决方案</p>
        </div>
    </div>
</body>
</html>`, time.Now().Format("2006-01-02 15:04:05"))
}

// GenerateCheckResultsTable 生成检查结果表格
func (hts *HTMLTemplateService) GenerateCheckResultsTable(checkResults []data.BaselineCheckResult, content string) string {
	if content == "仅摘要信息" || len(checkResults) == 0 {
		return ""
	}

	var buffer strings.Builder
	buffer.WriteString(`
                <table class="check-table">
                    <thead>
                        <tr>
                            <th>检查项</th>
                            <th>类别</th>
                            <th>结果</th>
                            <th>风险等级</th>
                            <th>详情</th>
                        </tr>
                    </thead>
                    <tbody>
`)

	for _, check := range checkResults {
		if content == "仅失败项目" && check.Status == "通过" {
			continue
		}

		statusClass := "status-pass"
		if check.Status == "失败" {
			statusClass = "status-fail"
		} else if check.Status == "警告" {
			statusClass = "status-warn"
		}

		riskClass := "risk-low"
		if check.Risk == "高" || check.Risk == "严重" {
			riskClass = "risk-high"
		} else if check.Risk == "中等" {
			riskClass = "risk-medium"
		}

		details := check.Details
		if details == "" {
			details = check.Description
		}

		buffer.WriteString(fmt.Sprintf(`
                        <tr>
                            <td>%s</td>
                            <td>%s</td>
                            <td class="%s">%s</td>
                            <td class="%s">%s</td>
                            <td>%s</td>
                        </tr>
`, check.CheckName, check.Category, statusClass, check.Status, riskClass, check.Risk, details))
	}

	buffer.WriteString(`
                    </tbody>
                </table>
`)

	return buffer.String()
}
