package data

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
)

// HostStorage 主机存储管理器 - 专门负责主机和主机组的存储
type HostStorage struct {
	dataDir string
	mutex   sync.RWMutex
}

// NewHostStorage 创建主机存储管理器
func NewHostStorage(dataDir string) *HostStorage {
	return &HostStorage{
		dataDir: dataDir,
	}
}

// SaveHosts 保存主机列表
func (hs *HostStorage) SaveHosts(hosts []HostInfo) error {
	hs.mutex.Lock()
	defer hs.mutex.Unlock()

	data, err := json.MarshalIndent(hosts, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化主机数据失败: %v", err)
	}

	hostsFile := filepath.Join(hs.dataDir, "hosts.json")
	return os.WriteFile(hostsFile, data, 0644)
}

// LoadHosts 加载主机列表
func (hs *HostStorage) LoadHosts() ([]HostInfo, error) {
	hs.mutex.RLock()
	defer hs.mutex.RUnlock()

	hostsFile := filepath.Join(hs.dataDir, "hosts.json")
	if _, err := os.Stat(hostsFile); os.IsNotExist(err) {
		return []HostInfo{}, nil
	}

	data, err := os.ReadFile(hostsFile)
	if err != nil {
		return nil, fmt.Errorf("读取主机文件失败: %v", err)
	}

	var hosts []HostInfo
	if err := json.Unmarshal(data, &hosts); err != nil {
		return nil, fmt.Errorf("解析主机数据失败: %v", err)
	}

	return hosts, nil
}

// SaveHost 保存单个主机
func (hs *HostStorage) SaveHost(host HostInfo) error {
	hosts, err := hs.LoadHosts()
	if err != nil {
		return err
	}

	// 检查是否已存在
	found := false
	for i, h := range hosts {
		if h.ID == host.ID {
			hosts[i] = host
			found = true
			break
		}
	}

	// 如果不存在则添加
	if !found {
		hosts = append(hosts, host)
	}

	return hs.SaveHosts(hosts)
}

// DeleteHost 删除主机
func (hs *HostStorage) DeleteHost(hostID string) error {
	hosts, err := hs.LoadHosts()
	if err != nil {
		return err
	}

	for i, host := range hosts {
		if host.ID == hostID {
			hosts = append(hosts[:i], hosts[i+1:]...)
			return hs.SaveHosts(hosts)
		}
	}

	return fmt.Errorf("主机 %s 不存在", hostID)
}

// GetHost 获取单个主机
func (hs *HostStorage) GetHost(hostID string) (*HostInfo, error) {
	hosts, err := hs.LoadHosts()
	if err != nil {
		return nil, err
	}

	for _, host := range hosts {
		if host.ID == hostID {
			return &host, nil
		}
	}

	return nil, fmt.Errorf("主机 %s 不存在", hostID)
}

// SaveGroups 保存主机组列表
func (hs *HostStorage) SaveGroups(groups []HostGroup) error {
	hs.mutex.Lock()
	defer hs.mutex.Unlock()

	data, err := json.MarshalIndent(groups, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化主机组数据失败: %v", err)
	}

	groupsFile := filepath.Join(hs.dataDir, "groups.json")
	return os.WriteFile(groupsFile, data, 0644)
}

// LoadGroups 加载主机组列表
func (hs *HostStorage) LoadGroups() ([]HostGroup, error) {
	hs.mutex.RLock()
	defer hs.mutex.RUnlock()

	groupsFile := filepath.Join(hs.dataDir, "groups.json")
	if _, err := os.Stat(groupsFile); os.IsNotExist(err) {
		return []HostGroup{}, nil
	}

	data, err := os.ReadFile(groupsFile)
	if err != nil {
		return nil, fmt.Errorf("读取主机组文件失败: %v", err)
	}

	var groups []HostGroup
	if err := json.Unmarshal(data, &groups); err != nil {
		return nil, fmt.Errorf("解析主机组数据失败: %v", err)
	}

	return groups, nil
}

// SaveGroup 保存单个主机组
func (hs *HostStorage) SaveGroup(group HostGroup) error {
	groups, err := hs.LoadGroups()
	if err != nil {
		return err
	}

	// 检查是否已存在
	found := false
	for i, g := range groups {
		if g.ID == group.ID {
			groups[i] = group
			found = true
			break
		}
	}

	// 如果不存在则添加
	if !found {
		groups = append(groups, group)
	}

	return hs.SaveGroups(groups)
}

// DeleteGroup 删除主机组
func (hs *HostStorage) DeleteGroup(groupID string) error {
	groups, err := hs.LoadGroups()
	if err != nil {
		return err
	}

	for i, group := range groups {
		if group.ID == groupID {
			groups = append(groups[:i], groups[i+1:]...)
			return hs.SaveGroups(groups)
		}
	}

	return fmt.Errorf("主机组 %s 不存在", groupID)
}

// GetGroup 获取单个主机组
func (hs *HostStorage) GetGroup(groupID string) (*HostGroup, error) {
	groups, err := hs.LoadGroups()
	if err != nil {
		return nil, err
	}

	for _, group := range groups {
		if group.ID == groupID {
			return &group, nil
		}
	}

	return nil, fmt.Errorf("主机组 %s 不存在", groupID)
}

// GetHostsByGroup 根据主机组获取主机列表
func (hs *HostStorage) GetHostsByGroup(groupID string) ([]HostInfo, error) {
	group, err := hs.GetGroup(groupID)
	if err != nil {
		return nil, err
	}

	hosts, err := hs.LoadHosts()
	if err != nil {
		return nil, err
	}

	var groupHosts []HostInfo
	for _, groupHost := range group.Hosts {
		for _, host := range hosts {
			if host.ID == groupHost.ID {
				groupHosts = append(groupHosts, host)
				break
			}
		}
	}

	return groupHosts, nil
}

// ValidateHost 验证主机数据
func (hs *HostStorage) ValidateHost(host HostInfo) error {
	if host.ID == "" {
		return fmt.Errorf("主机ID不能为空")
	}
	if host.Name == "" {
		return fmt.Errorf("主机名不能为空")
	}
	if host.Host == "" {
		return fmt.Errorf("主机地址不能为空")
	}
	return nil
}

// ValidateGroup 验证主机组数据
func (hs *HostStorage) ValidateGroup(group HostGroup) error {
	if group.ID == "" {
		return fmt.Errorf("主机组ID不能为空")
	}
	if group.Name == "" {
		return fmt.Errorf("主机组名不能为空")
	}
	return nil
}
