package data

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// ResultStorage 结果存储管理器 - 专门负责扫描结果的存储
type ResultStorage struct {
	dataDir string
	mutex   sync.RWMutex
}

// NewResultStorage 创建结果存储管理器
func NewResultStorage(dataDir string) *ResultStorage {
	return &ResultStorage{
		dataDir: dataDir,
	}
}

// SaveScanResult 保存扫描结果
func (rs *ResultStorage) SaveScanResult(result ScanResult) error {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	// 创建结果目录
	resultDir := filepath.Join(rs.dataDir, "results")
	if err := os.MkdirAll(resultDir, 0755); err != nil {
		return fmt.Errorf("创建结果目录失败: %v", err)
	}

	// 生成文件名
	filename := fmt.Sprintf("%s_%s.json", result.HostID, result.StartTime.Format("20060102_150405"))
	resultFile := filepath.Join(resultDir, filename)

	data, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化扫描结果失败: %v", err)
	}

	return os.WriteFile(resultFile, data, 0644)
}

// SaveBatchScanResult 保存批量扫描结果
func (rs *ResultStorage) SaveBatchScanResult(result BatchScanResult) error {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	// 创建批量结果目录
	batchDir := filepath.Join(rs.dataDir, "batch_results")
	if err := os.MkdirAll(batchDir, 0755); err != nil {
		return fmt.Errorf("创建批量结果目录失败: %v", err)
	}

	// 生成文件名
	filename := fmt.Sprintf("batch_%s_%s.json", result.ID, result.StartTime.Format("20060102_150405"))
	resultFile := filepath.Join(batchDir, filename)

	data, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化批量扫描结果失败: %v", err)
	}

	return os.WriteFile(resultFile, data, 0644)
}

// LoadScanResults 加载扫描结果
func (rs *ResultStorage) LoadScanResults() ([]ScanResult, error) {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()

	resultDir := filepath.Join(rs.dataDir, "results")
	if _, err := os.Stat(resultDir); os.IsNotExist(err) {
		return []ScanResult{}, nil
	}

	files, err := os.ReadDir(resultDir)
	if err != nil {
		return nil, fmt.Errorf("读取结果目录失败: %v", err)
	}

	var results []ScanResult
	for _, file := range files {
		if filepath.Ext(file.Name()) != ".json" {
			continue
		}

		resultFile := filepath.Join(resultDir, file.Name())
		data, err := os.ReadFile(resultFile)
		if err != nil {
			continue // 跳过无法读取的文件
		}

		var result ScanResult
		if err := json.Unmarshal(data, &result); err != nil {
			continue // 跳过无法解析的文件
		}

		results = append(results, result)
	}

	return results, nil
}

// LoadBatchScanResults 加载批量扫描结果
func (rs *ResultStorage) LoadBatchScanResults() ([]BatchScanResult, error) {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()

	batchDir := filepath.Join(rs.dataDir, "batch_results")
	if _, err := os.Stat(batchDir); os.IsNotExist(err) {
		return []BatchScanResult{}, nil
	}

	files, err := os.ReadDir(batchDir)
	if err != nil {
		return nil, fmt.Errorf("读取批量结果目录失败: %v", err)
	}

	var results []BatchScanResult
	for _, file := range files {
		if filepath.Ext(file.Name()) != ".json" {
			continue
		}

		resultFile := filepath.Join(batchDir, file.Name())
		data, err := os.ReadFile(resultFile)
		if err != nil {
			continue // 跳过无法读取的文件
		}

		var result BatchScanResult
		if err := json.Unmarshal(data, &result); err != nil {
			continue // 跳过无法解析的文件
		}

		results = append(results, result)
	}

	return results, nil
}

// GetResultsByHost 根据主机ID获取扫描结果
func (rs *ResultStorage) GetResultsByHost(hostID string) ([]ScanResult, error) {
	results, err := rs.LoadScanResults()
	if err != nil {
		return nil, err
	}

	var hostResults []ScanResult
	for _, result := range results {
		if result.HostID == hostID {
			hostResults = append(hostResults, result)
		}
	}

	return hostResults, nil
}

// GetResultsByTask 根据任务ID获取批量扫描结果
func (rs *ResultStorage) GetResultsByTask(taskID string) ([]BatchScanResult, error) {
	results, err := rs.LoadBatchScanResults()
	if err != nil {
		return nil, err
	}

	var taskResults []BatchScanResult
	for _, result := range results {
		if result.ID == taskID {
			taskResults = append(taskResults, result)
		}
	}

	return taskResults, nil
}

// CleanupOldResults 清理旧的扫描结果
func (rs *ResultStorage) CleanupOldResults(days int) error {
	cutoff := time.Now().AddDate(0, 0, -days)

	// 清理单个扫描结果
	if err := rs.cleanupResultsInDir(filepath.Join(rs.dataDir, "results"), cutoff); err != nil {
		return fmt.Errorf("清理扫描结果失败: %v", err)
	}

	// 清理批量扫描结果
	if err := rs.cleanupResultsInDir(filepath.Join(rs.dataDir, "batch_results"), cutoff); err != nil {
		return fmt.Errorf("清理批量扫描结果失败: %v", err)
	}

	return nil
}

// cleanupResultsInDir 清理指定目录中的旧结果
func (rs *ResultStorage) cleanupResultsInDir(dir string, cutoff time.Time) error {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return nil // 目录不存在，无需清理
	}

	files, err := os.ReadDir(dir)
	if err != nil {
		return err
	}

	for _, file := range files {
		if filepath.Ext(file.Name()) != ".json" {
			continue
		}

		info, err := file.Info()
		if err != nil {
			continue
		}

		// 如果文件修改时间早于截止时间，则删除
		if info.ModTime().Before(cutoff) {
			filePath := filepath.Join(dir, file.Name())
			if err := os.Remove(filePath); err != nil {
				// 记录错误但继续处理其他文件
				fmt.Printf("删除文件 %s 失败: %v\n", filePath, err)
			}
		}
	}

	return nil
}

// ExportResults 导出扫描结果
func (rs *ResultStorage) ExportResults(results []ScanResult, filename string) error {
	if filename == "" {
		filename = fmt.Sprintf("scan_results_%s.json", time.Now().Format("20060102_150405"))
	}

	data, err := json.MarshalIndent(results, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化导出数据失败: %v", err)
	}

	exportFile := filepath.Join(rs.dataDir, "exports", filename)
	if err := os.MkdirAll(filepath.Dir(exportFile), 0755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}

	return os.WriteFile(exportFile, data, 0644)
}

// GetResultStatistics 获取结果统计信息
func (rs *ResultStorage) GetResultStatistics() (*ResultStatistics, error) {
	results, err := rs.LoadScanResults()
	if err != nil {
		return nil, err
	}

	batchResults, err := rs.LoadBatchScanResults()
	if err != nil {
		return nil, err
	}

	stats := &ResultStatistics{
		TotalResults:      len(results),
		TotalBatchResults: len(batchResults),
		LatestScan:        time.Time{},
	}

	// 找到最新的扫描时间
	for _, result := range results {
		if result.StartTime.After(stats.LatestScan) {
			stats.LatestScan = result.StartTime
		}
	}

	for _, result := range batchResults {
		if result.StartTime.After(stats.LatestScan) {
			stats.LatestScan = result.StartTime
		}
	}

	return stats, nil
}

// ResultStatistics 结果统计信息
type ResultStatistics struct {
	TotalResults      int       `json:"totalResults"`
	TotalBatchResults int       `json:"totalBatchResults"`
	LatestScan        time.Time `json:"latestScan"`
}
