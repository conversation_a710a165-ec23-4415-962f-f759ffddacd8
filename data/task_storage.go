package data

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// TaskStorage 任务存储管理器 - 专门负责扫描任务的存储
type TaskStorage struct {
	dataDir string
	mutex   sync.RWMutex
}

// NewTaskStorage 创建任务存储管理器
func NewTaskStorage(dataDir string) *TaskStorage {
	return &TaskStorage{
		dataDir: dataDir,
	}
}

// SaveTasks 保存任务列表
func (ts *TaskStorage) SaveTasks(tasks []ScanTask) error {
	ts.mutex.Lock()
	defer ts.mutex.Unlock()

	data, err := json.MarshalIndent(tasks, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化任务数据失败: %v", err)
	}

	tasksFile := filepath.Join(ts.dataDir, "tasks.json")
	return os.WriteFile(tasksFile, data, 0644)
}

// LoadTasks 加载任务列表
func (ts *TaskStorage) LoadTasks() ([]ScanTask, error) {
	ts.mutex.RLock()
	defer ts.mutex.RUnlock()

	tasksFile := filepath.Join(ts.dataDir, "tasks.json")
	if _, err := os.Stat(tasksFile); os.IsNotExist(err) {
		return []ScanTask{}, nil
	}

	data, err := os.ReadFile(tasksFile)
	if err != nil {
		return nil, fmt.Errorf("读取任务文件失败: %v", err)
	}

	var tasks []ScanTask
	if err := json.Unmarshal(data, &tasks); err != nil {
		return nil, fmt.Errorf("解析任务数据失败: %v", err)
	}

	return tasks, nil
}

// SaveTask 保存单个任务
func (ts *TaskStorage) SaveTask(task ScanTask) error {
	tasks, err := ts.LoadTasks()
	if err != nil {
		return err
	}

	// 检查是否已存在
	found := false
	for i, t := range tasks {
		if t.ID == task.ID {
			tasks[i] = task
			found = true
			break
		}
	}

	// 如果不存在则添加
	if !found {
		tasks = append(tasks, task)
	}

	return ts.SaveTasks(tasks)
}

// DeleteTask 删除任务
func (ts *TaskStorage) DeleteTask(taskID string) error {
	tasks, err := ts.LoadTasks()
	if err != nil {
		return err
	}

	for i, task := range tasks {
		if task.ID == taskID {
			tasks = append(tasks[:i], tasks[i+1:]...)
			return ts.SaveTasks(tasks)
		}
	}

	return fmt.Errorf("任务 %s 不存在", taskID)
}

// GetTask 获取单个任务
func (ts *TaskStorage) GetTask(taskID string) (*ScanTask, error) {
	tasks, err := ts.LoadTasks()
	if err != nil {
		return nil, err
	}

	for _, task := range tasks {
		if task.ID == taskID {
			return &task, nil
		}
	}

	return nil, fmt.Errorf("任务 %s 不存在", taskID)
}

// UpdateTaskStatus 更新任务状态
func (ts *TaskStorage) UpdateTaskStatus(taskID, status string) error {
	task, err := ts.GetTask(taskID)
	if err != nil {
		return err
	}

	task.Status = status
	// UpdatedAt字段不存在，使用其他时间字段
	if status == "运行中" && task.StartedAt == nil {
		now := time.Now()
		task.StartedAt = &now
	} else if status == "已完成" && task.CompletedAt == nil {
		now := time.Now()
		task.CompletedAt = &now
	}

	return ts.SaveTask(*task)
}

// UpdateTaskProgress 更新任务进度
func (ts *TaskStorage) UpdateTaskProgress(taskID string, progress float64) error {
	task, err := ts.GetTask(taskID)
	if err != nil {
		return err
	}

	task.Progress = progress
	// UpdatedAt字段不存在，不需要更新时间

	return ts.SaveTask(*task)
}

// GetTasksByStatus 根据状态获取任务
func (ts *TaskStorage) GetTasksByStatus(status string) ([]ScanTask, error) {
	tasks, err := ts.LoadTasks()
	if err != nil {
		return nil, err
	}

	var filteredTasks []ScanTask
	for _, task := range tasks {
		if task.Status == status {
			filteredTasks = append(filteredTasks, task)
		}
	}

	return filteredTasks, nil
}

// GetRunningTasks 获取正在运行的任务
func (ts *TaskStorage) GetRunningTasks() ([]ScanTask, error) {
	return ts.GetTasksByStatus("运行中")
}

// GetCompletedTasks 获取已完成的任务
func (ts *TaskStorage) GetCompletedTasks() ([]ScanTask, error) {
	return ts.GetTasksByStatus("已完成")
}

// CleanupOldTasks 清理旧任务
func (ts *TaskStorage) CleanupOldTasks(days int) error {
	tasks, err := ts.LoadTasks()
	if err != nil {
		return err
	}

	cutoff := time.Now().AddDate(0, 0, -days)
	var activeTasks []ScanTask

	for _, task := range tasks {
		// 保留最近的任务或正在运行的任务
		// 使用CreatedAt字段代替UpdatedAt
		if task.CreatedAt.After(cutoff) || task.Status == "运行中" {
			activeTasks = append(activeTasks, task)
		}
	}

	return ts.SaveTasks(activeTasks)
}

// ValidateTask 验证任务数据
func (ts *TaskStorage) ValidateTask(task ScanTask) error {
	if task.ID == "" {
		return fmt.Errorf("任务ID不能为空")
	}
	if task.Name == "" {
		return fmt.Errorf("任务名不能为空")
	}
	if len(task.HostIDs) == 0 {
		return fmt.Errorf("任务必须包含至少一个主机")
	}
	return nil
}

// GetTaskStatistics 获取任务统计信息
func (ts *TaskStorage) GetTaskStatistics() (*TaskStatistics, error) {
	tasks, err := ts.LoadTasks()
	if err != nil {
		return nil, err
	}

	stats := &TaskStatistics{
		Total:     len(tasks),
		Running:   0,
		Completed: 0,
		Failed:    0,
		Pending:   0,
	}

	for _, task := range tasks {
		switch task.Status {
		case "运行中":
			stats.Running++
		case "已完成":
			stats.Completed++
		case "失败":
			stats.Failed++
		case "待执行":
			stats.Pending++
		}
	}

	return stats, nil
}

// TaskStatistics 任务统计信息
type TaskStatistics struct {
	Total     int `json:"total"`
	Running   int `json:"running"`
	Completed int `json:"completed"`
	Failed    int `json:"failed"`
	Pending   int `json:"pending"`
}
