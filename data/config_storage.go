package data

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
)

// ConfigStorage 配置存储管理器 - 专门负责应用配置的存储
type ConfigStorage struct {
	dataDir string
	mutex   sync.RWMutex
}

// NewConfigStorage 创建配置存储管理器
func NewConfigStorage(dataDir string) *ConfigStorage {
	return &ConfigStorage{
		dataDir: dataDir,
	}
}

// SaveConfig 保存应用配置
func (cs *ConfigStorage) SaveConfig(config AppConfig) error {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置数据失败: %v", err)
	}

	configFile := filepath.Join(cs.dataDir, "config.json")
	return os.WriteFile(configFile, data, 0644)
}

// LoadConfig 加载应用配置
func (cs *ConfigStorage) LoadConfig() (*AppConfig, error) {
	cs.mutex.RLock()
	defer cs.mutex.RUnlock()

	configFile := filepath.Join(cs.dataDir, "config.json")
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		// 返回默认配置
		return cs.getDefaultConfig(), nil
	}

	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config AppConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置数据失败: %v", err)
	}

	// 合并默认配置，确保所有字段都有值
	defaultConfig := cs.getDefaultConfig()
	cs.mergeConfig(&config, defaultConfig)

	return &config, nil
}

// getDefaultConfig 获取默认配置
func (cs *ConfigStorage) getDefaultConfig() *AppConfig {
	return &AppConfig{
		Version:         "3.2.0",
		DataDir:         "./data",
		LogLevel:        "info",
		MaxConcurrency:  10,
		SSHTimeout:      30,
		ScanTimeout:     60,
		AutoSave:        true,
		BackupEnabled:   true,
		BackupInterval:  24,
		Theme:           "light",
	}
}

// mergeConfig 合并配置，用默认值填充缺失的字段
func (cs *ConfigStorage) mergeConfig(config, defaultConfig *AppConfig) {
	if config.Version == "" {
		config.Version = defaultConfig.Version
	}
	if config.DataDir == "" {
		config.DataDir = defaultConfig.DataDir
	}
	if config.LogLevel == "" {
		config.LogLevel = defaultConfig.LogLevel
	}
	if config.MaxConcurrency == 0 {
		config.MaxConcurrency = defaultConfig.MaxConcurrency
	}
	if config.SSHTimeout == 0 {
		config.SSHTimeout = defaultConfig.SSHTimeout
	}
	if config.ScanTimeout == 0 {
		config.ScanTimeout = defaultConfig.ScanTimeout
	}
	if config.BackupInterval == 0 {
		config.BackupInterval = defaultConfig.BackupInterval
	}
	if config.Theme == "" {
		config.Theme = defaultConfig.Theme
	}
}

// UpdateConfig 更新配置的特定字段
func (cs *ConfigStorage) UpdateConfig(updates map[string]interface{}) error {
	config, err := cs.LoadConfig()
	if err != nil {
		return err
	}

	// 更新指定字段
	for key, value := range updates {
		switch key {
		case "version":
			if v, ok := value.(string); ok {
				config.Version = v
			}
		case "dataDir":
			if v, ok := value.(string); ok {
				config.DataDir = v
			}
		case "logLevel":
			if v, ok := value.(string); ok {
				config.LogLevel = v
			}
		case "maxConcurrency":
			if v, ok := value.(int); ok {
				config.MaxConcurrency = v
			}
		case "sshTimeout":
			if v, ok := value.(int); ok {
				config.SSHTimeout = v
			}
		case "scanTimeout":
			if v, ok := value.(int); ok {
				config.ScanTimeout = v
			}
		case "autoSave":
			if v, ok := value.(bool); ok {
				config.AutoSave = v
			}
		case "backupEnabled":
			if v, ok := value.(bool); ok {
				config.BackupEnabled = v
			}
		case "backupInterval":
			if v, ok := value.(int); ok {
				config.BackupInterval = v
			}
		case "theme":
			if v, ok := value.(string); ok {
				config.Theme = v
			}

		}
	}

	return cs.SaveConfig(*config)
}

// ResetConfig 重置配置为默认值
func (cs *ConfigStorage) ResetConfig() error {
	defaultConfig := cs.getDefaultConfig()
	return cs.SaveConfig(*defaultConfig)
}

// ValidateConfig 验证配置数据
func (cs *ConfigStorage) ValidateConfig(config AppConfig) error {
	if config.MaxConcurrency < 1 || config.MaxConcurrency > 100 {
		return fmt.Errorf("最大并发数必须在1-100之间")
	}
	if config.SSHTimeout < 5 || config.SSHTimeout > 300 {
		return fmt.Errorf("SSH超时时间必须在5-300秒之间")
	}
	if config.ScanTimeout < 10 || config.ScanTimeout > 600 {
		return fmt.Errorf("扫描超时时间必须在10-600秒之间")
	}
	if config.BackupInterval < 1 || config.BackupInterval > 168 {
		return fmt.Errorf("备份间隔必须在1-168小时之间")
	}

	validThemes := []string{"light", "dark", "auto"}
	validTheme := false
	for _, theme := range validThemes {
		if config.Theme == theme {
			validTheme = true
			break
		}
	}
	if !validTheme {
		return fmt.Errorf("主题必须是 light、dark 或 auto")
	}

	validLanguages := []string{"zh-CN", "en-US"}
	validLanguage := false
	for _, lang := range validLanguages {
		if config.Language == lang {
			validLanguage = true
			break
		}
	}
	if !validLanguage {
		return fmt.Errorf("语言必须是 zh-CN 或 en-US")
	}

	validLogLevels := []string{"debug", "info", "warn", "error"}
	validLogLevel := false
	for _, level := range validLogLevels {
		if config.LogLevel == level {
			validLogLevel = true
			break
		}
	}
	if !validLogLevel {
		return fmt.Errorf("日志级别必须是 debug、info、warn 或 error")
	}



	return nil
}

// BackupConfig 备份当前配置
func (cs *ConfigStorage) BackupConfig() error {
	config, err := cs.LoadConfig()
	if err != nil {
		return err
	}

	backupFile := filepath.Join(cs.dataDir, "config_backup.json")
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化备份配置失败: %v", err)
	}

	return os.WriteFile(backupFile, data, 0644)
}

// RestoreConfig 从备份恢复配置
func (cs *ConfigStorage) RestoreConfig() error {
	backupFile := filepath.Join(cs.dataDir, "config_backup.json")
	if _, err := os.Stat(backupFile); os.IsNotExist(err) {
		return fmt.Errorf("备份配置文件不存在")
	}

	data, err := os.ReadFile(backupFile)
	if err != nil {
		return fmt.Errorf("读取备份配置失败: %v", err)
	}

	var config AppConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析备份配置失败: %v", err)
	}

	return cs.SaveConfig(config)
}
