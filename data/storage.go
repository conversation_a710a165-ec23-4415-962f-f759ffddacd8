package data

import (
	"fmt"
	"os"
	"path/filepath"
)

// Storage 数据存储管理器 - 模块化架构，委托给专门的存储模块
type Storage struct {
	dataDir string

	// 专门的存储模块
	hostStorage   *HostStorage
	taskStorage   *TaskStorage
	resultStorage *ResultStorage
	configStorage *ConfigStorage
}

// NewStorage 创建存储管理器 - 使用模块化架构
func NewStorage(dataDir string) *Storage {
	if dataDir == "" {
		// 获取程序运行目录
		if exePath, err := os.Executable(); err == nil {
			dataDir = filepath.Dir(exePath)
		} else {
			dataDir = "."
		}
	}

	// 确保数据目录存在
	os.MkdirAll(dataDir, 0755)

	return &Storage{
		dataDir:       dataDir,
		hostStorage:   NewHostStorage(dataDir),
		taskStorage:   NewTaskStorage(dataDir),
		resultStorage: NewResultStorage(dataDir),
		configStorage: NewConfigStorage(dataDir),
	}
}

// GetDataDir 获取数据目录
func (s *Storage) GetDataDir() string {
	return s.dataDir
}

// ========== 主机管理委托方法 ==========

// SaveHosts 保存主机列表 - 委托给主机存储模块
func (s *Storage) SaveHosts(hosts []HostInfo) error {
	return s.hostStorage.SaveHosts(hosts)
}

// LoadHosts 加载主机列表 - 委托给主机存储模块
func (s *Storage) LoadHosts() ([]HostInfo, error) {
	return s.hostStorage.LoadHosts()
}

// SaveHost 保存单个主机 - 委托给主机存储模块
func (s *Storage) SaveHost(host HostInfo) error {
	return s.hostStorage.SaveHost(host)
}

// DeleteHost 删除主机 - 委托给主机存储模块
func (s *Storage) DeleteHost(hostID string) error {
	return s.hostStorage.DeleteHost(hostID)
}

// GetHost 获取单个主机 - 委托给主机存储模块
func (s *Storage) GetHost(hostID string) (*HostInfo, error) {
	return s.hostStorage.GetHost(hostID)
}

// SaveGroups 保存主机组列表 - 委托给主机存储模块
func (s *Storage) SaveGroups(groups []HostGroup) error {
	return s.hostStorage.SaveGroups(groups)
}

// LoadGroups 加载主机组列表 - 委托给主机存储模块
func (s *Storage) LoadGroups() ([]HostGroup, error) {
	return s.hostStorage.LoadGroups()
}

// SaveGroup 保存单个主机组 - 委托给主机存储模块
func (s *Storage) SaveGroup(group HostGroup) error {
	return s.hostStorage.SaveGroup(group)
}

// DeleteGroup 删除主机组 - 委托给主机存储模块
func (s *Storage) DeleteGroup(groupID string) error {
	return s.hostStorage.DeleteGroup(groupID)
}

// GetGroup 获取单个主机组 - 委托给主机存储模块
func (s *Storage) GetGroup(groupID string) (*HostGroup, error) {
	return s.hostStorage.GetGroup(groupID)
}

// ========== 任务管理委托方法 ==========

// SaveTasks 保存任务列表 - 委托给任务存储模块
func (s *Storage) SaveTasks(tasks []ScanTask) error {
	return s.taskStorage.SaveTasks(tasks)
}

// LoadTasks 加载任务列表 - 委托给任务存储模块
func (s *Storage) LoadTasks() ([]ScanTask, error) {
	return s.taskStorage.LoadTasks()
}

// SaveTask 保存单个任务 - 委托给任务存储模块
func (s *Storage) SaveTask(task ScanTask) error {
	return s.taskStorage.SaveTask(task)
}

// DeleteTask 删除任务 - 委托给任务存储模块
func (s *Storage) DeleteTask(taskID string) error {
	return s.taskStorage.DeleteTask(taskID)
}

// GetTask 获取单个任务 - 委托给任务存储模块
func (s *Storage) GetTask(taskID string) (*ScanTask, error) {
	return s.taskStorage.GetTask(taskID)
}

// ========== 结果管理委托方法 ==========

// SaveScanResult 保存扫描结果 - 委托给结果存储模块
func (s *Storage) SaveScanResult(result ScanResult) error {
	return s.resultStorage.SaveScanResult(result)
}

// SaveBatchScanResult 保存批量扫描结果 - 委托给结果存储模块
func (s *Storage) SaveBatchScanResult(result BatchScanResult) error {
	return s.resultStorage.SaveBatchScanResult(result)
}

// ExportResults 导出扫描结果 - 委托给结果存储模块
func (s *Storage) ExportResults(results []ScanResult, filename string) error {
	return s.resultStorage.ExportResults(results, filename)
}

// ========== 配置管理委托方法 ==========

// SaveConfig 保存应用配置 - 委托给配置存储模块
func (s *Storage) SaveConfig(config AppConfig) error {
	return s.configStorage.SaveConfig(config)
}

// LoadConfig 加载应用配置 - 委托给配置存储模块
func (s *Storage) LoadConfig() (*AppConfig, error) {
	return s.configStorage.LoadConfig()
}

// ========== 统计信息方法 ==========

// GetStatistics 获取统计信息 - 聚合各模块的统计数据
func (s *Storage) GetStatistics() (*Statistics, error) {
	// 获取主机统计
	hosts, err := s.hostStorage.LoadHosts()
	if err != nil {
		return nil, err
	}

	groups, err := s.hostStorage.LoadGroups()
	if err != nil {
		return nil, err
	}

	// 获取任务统计（暂时不使用）
	_, err = s.taskStorage.GetTaskStatistics()
	if err != nil {
		return nil, err
	}

	// 获取结果统计
	resultStats, err := s.resultStorage.GetResultStatistics()
	if err != nil {
		return nil, err
	}

	return &Statistics{
		TotalHosts:       len(hosts),
		TotalGroups:      len(groups),
		TotalScans:       resultStats.TotalResults,
		LastScanTime:     resultStats.LatestScan,
		TotalChecks:      0, // 可以后续实现
		PassedChecks:     0, // 可以后续实现
		FailedChecks:     0, // 可以后续实现
		WarningChecks:    0, // 可以后续实现
		HighRiskIssues:   0, // 可以后续实现
		MediumRiskIssues: 0, // 可以后续实现
		LowRiskIssues:    0, // 可以后续实现
	}, nil
}

// CleanupOldScans 清理旧扫描数据 - 委托给各存储模块
func (s *Storage) CleanupOldScans(days int) error {
	// 清理旧任务
	if err := s.taskStorage.CleanupOldTasks(days); err != nil {
		return fmt.Errorf("清理旧任务失败: %v", err)
	}

	// 清理旧结果
	if err := s.resultStorage.CleanupOldResults(days); err != nil {
		return fmt.Errorf("清理旧结果失败: %v", err)
	}

	return nil
}
