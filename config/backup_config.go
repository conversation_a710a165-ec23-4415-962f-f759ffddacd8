package config

import (
	"time"

	"lcheck/data"
)

// BackupConfigManager 备份配置管理器 - 专门负责备份相关的配置
type BackupConfigManager struct {
	config *data.AppConfig
}

// NewBackupConfigManager 创建备份配置管理器
func NewBackupConfigManager(config *data.AppConfig) *BackupConfigManager {
	return &BackupConfigManager{
		config: config,
	}
}

// GetBackupEnabled 获取备份启用状态
func (bcm *BackupConfigManager) GetBackupEnabled() bool {
	return bcm.config.BackupEnabled
}

// SetBackupEnabled 设置备份启用状态
func (bcm *BackupConfigManager) SetBackupEnabled(enabled bool) {
	bcm.config.BackupEnabled = enabled
}

// GetBackupInterval 获取备份间隔（小时）
func (bcm *BackupConfigManager) GetBackupInterval() int {
	return bcm.config.BackupInterval
}

// SetBackupInterval 设置备份间隔（小时）
func (bcm *BackupConfigManager) SetBackupInterval(hours int) {
	if hours < 1 {
		hours = 1
	} else if hours > 168 { // 最多一周
		hours = 168
	}
	bcm.config.BackupInterval = hours
}

// GetBackupPath 获取备份路径
func (bcm *BackupConfigManager) GetBackupPath() string {
	if path := bcm.config.CustomSettings["backup_path"]; path != "" {
		return path
	}
	return "./backups"
}

// SetBackupPath 设置备份路径
func (bcm *BackupConfigManager) SetBackupPath(path string) {
	if bcm.config.CustomSettings == nil {
		bcm.config.CustomSettings = make(map[string]string)
	}
	bcm.config.CustomSettings["backup_path"] = path
}

// GetMaxBackupFiles 获取最大备份文件数
func (bcm *BackupConfigManager) GetMaxBackupFiles() int {
	if maxFiles := bcm.config.CustomSettings["max_backup_files"]; maxFiles != "" {
		if m := parseInt(maxFiles, 10); m >= 1 && m <= 100 {
			return m
		}
	}
	return 10
}

// SetMaxBackupFiles 设置最大备份文件数
func (bcm *BackupConfigManager) SetMaxBackupFiles(maxFiles int) {
	if maxFiles < 1 {
		maxFiles = 1
	} else if maxFiles > 100 {
		maxFiles = 100
	}
	
	if bcm.config.CustomSettings == nil {
		bcm.config.CustomSettings = make(map[string]string)
	}
	bcm.config.CustomSettings["max_backup_files"] = intToString(maxFiles)
}

// GetBackupCompression 获取备份压缩启用状态
func (bcm *BackupConfigManager) GetBackupCompression() bool {
	return bcm.config.CustomSettings["backup_compression"] != "false"
}

// SetBackupCompression 设置备份压缩启用状态
func (bcm *BackupConfigManager) SetBackupCompression(enabled bool) {
	if bcm.config.CustomSettings == nil {
		bcm.config.CustomSettings = make(map[string]string)
	}
	if enabled {
		bcm.config.CustomSettings["backup_compression"] = "true"
	} else {
		bcm.config.CustomSettings["backup_compression"] = "false"
	}
}

// GetBackupEncryption 获取备份加密启用状态
func (bcm *BackupConfigManager) GetBackupEncryption() bool {
	return bcm.config.CustomSettings["backup_encryption"] == "true"
}

// SetBackupEncryption 设置备份加密启用状态
func (bcm *BackupConfigManager) SetBackupEncryption(enabled bool) {
	if bcm.config.CustomSettings == nil {
		bcm.config.CustomSettings = make(map[string]string)
	}
	if enabled {
		bcm.config.CustomSettings["backup_encryption"] = "true"
	} else {
		bcm.config.CustomSettings["backup_encryption"] = "false"
	}
}

// GetBackupPassword 获取备份密码
func (bcm *BackupConfigManager) GetBackupPassword() string {
	return bcm.config.CustomSettings["backup_password"]
}

// SetBackupPassword 设置备份密码
func (bcm *BackupConfigManager) SetBackupPassword(password string) {
	if bcm.config.CustomSettings == nil {
		bcm.config.CustomSettings = make(map[string]string)
	}
	bcm.config.CustomSettings["backup_password"] = password
}

// GetAutoCleanup 获取自动清理启用状态
func (bcm *BackupConfigManager) GetAutoCleanup() bool {
	return bcm.config.CustomSettings["auto_cleanup"] != "false"
}

// SetAutoCleanup 设置自动清理启用状态
func (bcm *BackupConfigManager) SetAutoCleanup(enabled bool) {
	if bcm.config.CustomSettings == nil {
		bcm.config.CustomSettings = make(map[string]string)
	}
	if enabled {
		bcm.config.CustomSettings["auto_cleanup"] = "true"
	} else {
		bcm.config.CustomSettings["auto_cleanup"] = "false"
	}
}

// GetCleanupDays 获取清理天数
func (bcm *BackupConfigManager) GetCleanupDays() int {
	if days := bcm.config.CustomSettings["cleanup_days"]; days != "" {
		if d := parseInt(days, 30); d >= 1 && d <= 365 {
			return d
		}
	}
	return 30
}

// SetCleanupDays 设置清理天数
func (bcm *BackupConfigManager) SetCleanupDays(days int) {
	if days < 1 {
		days = 1
	} else if days > 365 {
		days = 365
	}
	
	if bcm.config.CustomSettings == nil {
		bcm.config.CustomSettings = make(map[string]string)
	}
	bcm.config.CustomSettings["cleanup_days"] = intToString(days)
}

// GetLastBackupTime 获取最后备份时间
func (bcm *BackupConfigManager) GetLastBackupTime() time.Time {
	if timeStr := bcm.config.CustomSettings["last_backup_time"]; timeStr != "" {
		if t, err := time.Parse(time.RFC3339, timeStr); err == nil {
			return t
		}
	}
	return time.Time{}
}

// SetLastBackupTime 设置最后备份时间
func (bcm *BackupConfigManager) SetLastBackupTime(t time.Time) {
	if bcm.config.CustomSettings == nil {
		bcm.config.CustomSettings = make(map[string]string)
	}
	bcm.config.CustomSettings["last_backup_time"] = t.Format(time.RFC3339)
}

// GetNextBackupTime 获取下次备份时间
func (bcm *BackupConfigManager) GetNextBackupTime() time.Time {
	lastBackup := bcm.GetLastBackupTime()
	if lastBackup.IsZero() {
		return time.Now().Add(time.Duration(bcm.GetBackupInterval()) * time.Hour)
	}
	return lastBackup.Add(time.Duration(bcm.GetBackupInterval()) * time.Hour)
}

// IsBackupDue 检查是否需要备份
func (bcm *BackupConfigManager) IsBackupDue() bool {
	if !bcm.GetBackupEnabled() {
		return false
	}
	return time.Now().After(bcm.GetNextBackupTime())
}

// GetBackupFilePattern 获取备份文件名模式
func (bcm *BackupConfigManager) GetBackupFilePattern() string {
	if pattern := bcm.config.CustomSettings["backup_file_pattern"]; pattern != "" {
		return pattern
	}
	return "lcheck_backup_%Y%m%d_%H%M%S"
}

// SetBackupFilePattern 设置备份文件名模式
func (bcm *BackupConfigManager) SetBackupFilePattern(pattern string) {
	if bcm.config.CustomSettings == nil {
		bcm.config.CustomSettings = make(map[string]string)
	}
	bcm.config.CustomSettings["backup_file_pattern"] = pattern
}

// Validate 验证备份配置
func (bcm *BackupConfigManager) Validate() []string {
	var errors []string
	
	// 验证备份间隔
	if bcm.config.BackupInterval < 1 || bcm.config.BackupInterval > 168 {
		errors = append(errors, "备份间隔必须在1-168小时之间")
	}
	
	// 验证最大备份文件数
	maxFiles := bcm.GetMaxBackupFiles()
	if maxFiles < 1 || maxFiles > 100 {
		errors = append(errors, "最大备份文件数必须在1-100之间")
	}
	
	// 验证清理天数
	cleanupDays := bcm.GetCleanupDays()
	if cleanupDays < 1 || cleanupDays > 365 {
		errors = append(errors, "清理天数必须在1-365之间")
	}
	
	// 验证备份路径
	if bcm.GetBackupPath() == "" {
		errors = append(errors, "备份路径不能为空")
	}
	
	// 验证加密配置
	if bcm.GetBackupEncryption() && bcm.GetBackupPassword() == "" {
		errors = append(errors, "启用备份加密时必须设置密码")
	}
	
	return errors
}

// GetBackupSettings 获取备份设置摘要
func (bcm *BackupConfigManager) GetBackupSettings() map[string]interface{} {
	return map[string]interface{}{
		"enabled":         bcm.GetBackupEnabled(),
		"interval":        bcm.GetBackupInterval(),
		"path":            bcm.GetBackupPath(),
		"maxFiles":        bcm.GetMaxBackupFiles(),
		"compression":     bcm.GetBackupCompression(),
		"encryption":      bcm.GetBackupEncryption(),
		"autoCleanup":     bcm.GetAutoCleanup(),
		"cleanupDays":     bcm.GetCleanupDays(),
		"lastBackupTime":  bcm.GetLastBackupTime(),
		"nextBackupTime":  bcm.GetNextBackupTime(),
		"isBackupDue":     bcm.IsBackupDue(),
		"filePattern":     bcm.GetBackupFilePattern(),
	}
}
