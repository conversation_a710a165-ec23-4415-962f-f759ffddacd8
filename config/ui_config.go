package config

import "lcheck/data"

// UIConfigManager UI配置管理器 - 专门负责用户界面相关的配置
type UIConfigManager struct {
	config *data.AppConfig
}

// NewUIConfigManager 创建UI配置管理器
func NewUIConfigManager(config *data.AppConfig) *UIConfigManager {
	return &UIConfigManager{
		config: config,
	}
}

// GetTheme 获取主题
func (ucm *UIConfigManager) GetTheme() string {
	return ucm.config.Theme
}

// SetTheme 设置主题
func (ucm *UIConfigManager) SetTheme(theme string) {
	validThemes := ucm.GetSupportedThemes()
	for _, validTheme := range validThemes {
		if theme == validTheme {
			ucm.config.Theme = theme
			return
		}
	}
}

// GetLanguage 获取语言
func (ucm *UIConfigManager) GetLanguage() string {
	return ucm.config.Language
}

// SetLanguage 设置语言
func (ucm *UIConfigManager) SetLanguage(language string) {
	validLanguages := ucm.GetSupportedLanguages()
	for _, validLanguage := range validLanguages {
		if language == validLanguage {
			ucm.config.Language = language
			return
		}
	}
}

// GetWindowSize 获取窗口大小
func (ucm *UIConfigManager) GetWindowSize() (int, int) {
	return ucm.config.WindowWidth, ucm.config.WindowHeight
}

// SetWindowSize 设置窗口大小
func (ucm *UIConfigManager) SetWindowSize(width, height int) {
	if width < 400 {
		width = 400
	} else if width > 3840 {
		width = 3840
	}
	
	if height < 300 {
		height = 300
	} else if height > 2160 {
		height = 2160
	}
	
	ucm.config.WindowWidth = width
	ucm.config.WindowHeight = height
}

// GetWindowPosition 获取窗口位置
func (ucm *UIConfigManager) GetWindowPosition() (int, int) {
	return ucm.config.WindowX, ucm.config.WindowY
}

// SetWindowPosition 设置窗口位置
func (ucm *UIConfigManager) SetWindowPosition(x, y int) {
	ucm.config.WindowX = x
	ucm.config.WindowY = y
}

// GetWindowMaximized 获取窗口最大化状态
func (ucm *UIConfigManager) GetWindowMaximized() bool {
	return ucm.config.CustomSettings["window_maximized"] == "true"
}

// SetWindowMaximized 设置窗口最大化状态
func (ucm *UIConfigManager) SetWindowMaximized(maximized bool) {
	if ucm.config.CustomSettings == nil {
		ucm.config.CustomSettings = make(map[string]string)
	}
	if maximized {
		ucm.config.CustomSettings["window_maximized"] = "true"
	} else {
		ucm.config.CustomSettings["window_maximized"] = "false"
	}
}

// GetFontSize 获取字体大小
func (ucm *UIConfigManager) GetFontSize() int {
	if fontSize := ucm.config.CustomSettings["font_size"]; fontSize != "" {
		if f := parseInt(fontSize, 12); f >= 8 && f <= 24 {
			return f
		}
	}
	return 12
}

// SetFontSize 设置字体大小
func (ucm *UIConfigManager) SetFontSize(size int) {
	if size < 8 {
		size = 8
	} else if size > 24 {
		size = 24
	}
	
	if ucm.config.CustomSettings == nil {
		ucm.config.CustomSettings = make(map[string]string)
	}
	ucm.config.CustomSettings["font_size"] = intToString(size)
}

// GetFontFamily 获取字体族
func (ucm *UIConfigManager) GetFontFamily() string {
	if fontFamily := ucm.config.CustomSettings["font_family"]; fontFamily != "" {
		return fontFamily
	}
	return "Microsoft YaHei"
}

// SetFontFamily 设置字体族
func (ucm *UIConfigManager) SetFontFamily(family string) {
	if ucm.config.CustomSettings == nil {
		ucm.config.CustomSettings = make(map[string]string)
	}
	ucm.config.CustomSettings["font_family"] = family
}

// GetShowToolbar 获取工具栏显示状态
func (ucm *UIConfigManager) GetShowToolbar() bool {
	return ucm.config.CustomSettings["show_toolbar"] != "false"
}

// SetShowToolbar 设置工具栏显示状态
func (ucm *UIConfigManager) SetShowToolbar(show bool) {
	if ucm.config.CustomSettings == nil {
		ucm.config.CustomSettings = make(map[string]string)
	}
	if show {
		ucm.config.CustomSettings["show_toolbar"] = "true"
	} else {
		ucm.config.CustomSettings["show_toolbar"] = "false"
	}
}

// GetShowStatusBar 获取状态栏显示状态
func (ucm *UIConfigManager) GetShowStatusBar() bool {
	return ucm.config.CustomSettings["show_statusbar"] != "false"
}

// SetShowStatusBar 设置状态栏显示状态
func (ucm *UIConfigManager) SetShowStatusBar(show bool) {
	if ucm.config.CustomSettings == nil {
		ucm.config.CustomSettings = make(map[string]string)
	}
	if show {
		ucm.config.CustomSettings["show_statusbar"] = "true"
	} else {
		ucm.config.CustomSettings["show_statusbar"] = "false"
	}
}

// GetAutoRefreshInterval 获取自动刷新间隔（秒）
func (ucm *UIConfigManager) GetAutoRefreshInterval() int {
	if interval := ucm.config.CustomSettings["auto_refresh_interval"]; interval != "" {
		if i := parseInt(interval, 30); i >= 5 && i <= 300 {
			return i
		}
	}
	return 30
}

// SetAutoRefreshInterval 设置自动刷新间隔（秒）
func (ucm *UIConfigManager) SetAutoRefreshInterval(seconds int) {
	if seconds < 5 {
		seconds = 5
	} else if seconds > 300 {
		seconds = 300
	}
	
	if ucm.config.CustomSettings == nil {
		ucm.config.CustomSettings = make(map[string]string)
	}
	ucm.config.CustomSettings["auto_refresh_interval"] = intToString(seconds)
}

// GetTablePageSize 获取表格分页大小
func (ucm *UIConfigManager) GetTablePageSize() int {
	if pageSize := ucm.config.CustomSettings["table_page_size"]; pageSize != "" {
		if p := parseInt(pageSize, 20); p >= 10 && p <= 100 {
			return p
		}
	}
	return 20
}

// SetTablePageSize 设置表格分页大小
func (ucm *UIConfigManager) SetTablePageSize(size int) {
	if size < 10 {
		size = 10
	} else if size > 100 {
		size = 100
	}
	
	if ucm.config.CustomSettings == nil {
		ucm.config.CustomSettings = make(map[string]string)
	}
	ucm.config.CustomSettings["table_page_size"] = intToString(size)
}

// GetSupportedThemes 获取支持的主题列表
func (ucm *UIConfigManager) GetSupportedThemes() []string {
	return []string{
		"light",
		"dark",
		"auto",
	}
}

// GetSupportedLanguages 获取支持的语言列表
func (ucm *UIConfigManager) GetSupportedLanguages() []string {
	return []string{
		"zh-CN",
		"en-US",
	}
}

// GetSupportedFontFamilies 获取支持的字体族列表
func (ucm *UIConfigManager) GetSupportedFontFamilies() []string {
	return []string{
		"Microsoft YaHei",
		"SimSun",
		"Arial",
		"Helvetica",
		"Times New Roman",
		"Courier New",
	}
}

// Validate 验证UI配置
func (ucm *UIConfigManager) Validate() []string {
	var errors []string
	
	// 验证主题
	validThemes := ucm.GetSupportedThemes()
	validTheme := false
	for _, theme := range validThemes {
		if ucm.config.Theme == theme {
			validTheme = true
			break
		}
	}
	if !validTheme {
		errors = append(errors, "无效的主题设置")
	}
	
	// 验证语言
	validLanguages := ucm.GetSupportedLanguages()
	validLanguage := false
	for _, language := range validLanguages {
		if ucm.config.Language == language {
			validLanguage = true
			break
		}
	}
	if !validLanguage {
		errors = append(errors, "无效的语言设置")
	}
	
	// 验证窗口大小
	if ucm.config.WindowWidth < 400 || ucm.config.WindowWidth > 3840 {
		errors = append(errors, "窗口宽度必须在400-3840之间")
	}
	if ucm.config.WindowHeight < 300 || ucm.config.WindowHeight > 2160 {
		errors = append(errors, "窗口高度必须在300-2160之间")
	}
	
	// 验证字体大小
	fontSize := ucm.GetFontSize()
	if fontSize < 8 || fontSize > 24 {
		errors = append(errors, "字体大小必须在8-24之间")
	}
	
	return errors
}

// GetUISettings 获取UI设置摘要
func (ucm *UIConfigManager) GetUISettings() map[string]interface{} {
	return map[string]interface{}{
		"theme":               ucm.GetTheme(),
		"language":            ucm.GetLanguage(),
		"windowWidth":         ucm.config.WindowWidth,
		"windowHeight":        ucm.config.WindowHeight,
		"windowX":             ucm.config.WindowX,
		"windowY":             ucm.config.WindowY,
		"windowMaximized":     ucm.GetWindowMaximized(),
		"fontSize":            ucm.GetFontSize(),
		"fontFamily":          ucm.GetFontFamily(),
		"showToolbar":         ucm.GetShowToolbar(),
		"showStatusBar":       ucm.GetShowStatusBar(),
		"autoRefreshInterval": ucm.GetAutoRefreshInterval(),
		"tablePageSize":       ucm.GetTablePageSize(),
	}
}
