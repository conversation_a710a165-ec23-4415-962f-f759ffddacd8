package config

import (
	"time"

	"lcheck/data"
)

// Config 配置管理器 - 模块化门面，委托给专门的配置模块
type Config struct {
	storage *data.Storage

	// 专门的配置管理器
	appConfig     *AppConfigManager
	networkConfig *NetworkConfigManager
	uiConfig      *UIConfigManager
	backupConfig  *BackupConfigManager
}

// NewConfig 创建配置管理器 - 使用模块化架构
func NewConfig(storage *data.Storage) *Config {
	// 创建应用配置管理器
	appConfig := NewAppConfigManager()

	// 创建其他配置管理器
	networkConfig := NewNetworkConfigManager(appConfig.GetConfig())
	uiConfig := NewUIConfigManager(appConfig.GetConfig())
	backupConfig := NewBackupConfigManager(appConfig.GetConfig())

	return &Config{
		storage:       storage,
		appConfig:     appConfig,
		networkConfig: networkConfig,
		uiConfig:      uiConfig,
		backupConfig:  backupConfig,
	}
}

// ========== 应用配置委托方法 ==========

// GetConfig 获取完整配置 - 委托给应用配置管理器
func (c *Config) GetConfig() *data.AppConfig {
	return c.appConfig.GetConfig()
}

// SaveConfig 保存配置 - 委托给存储层
func (c *Config) SaveConfig() error {
	return c.storage.SaveConfig(*c.appConfig.GetConfig())
}

// GetVersion 获取版本 - 委托给应用配置管理器
func (c *Config) GetVersion() string {
	return c.appConfig.GetVersion()
}

// GetDataDir 获取数据目录 - 委托给应用配置管理器
func (c *Config) GetDataDir() string {
	return c.appConfig.GetDataDir()
}

// GetLogLevel 获取日志级别 - 委托给应用配置管理器
func (c *Config) GetLogLevel() string {
	return c.appConfig.GetLogLevel()
}

// SetLogLevel 设置日志级别 - 委托给应用配置管理器
func (c *Config) SetLogLevel(level string) {
	c.appConfig.SetLogLevel(level)
}

// GetMaxConcurrency 获取最大并发数 - 委托给应用配置管理器
func (c *Config) GetMaxConcurrency() int {
	return c.appConfig.GetMaxConcurrency()
}

// SetMaxConcurrency 设置最大并发数 - 委托给应用配置管理器
func (c *Config) SetMaxConcurrency(concurrency int) {
	c.appConfig.SetMaxConcurrency(concurrency)
}

// GetAutoSave 获取自动保存设置 - 委托给应用配置管理器
func (c *Config) GetAutoSave() bool {
	return c.appConfig.GetAutoSave()
}

// SetAutoSave 设置自动保存 - 委托给应用配置管理器
func (c *Config) SetAutoSave(enabled bool) {
	c.appConfig.SetAutoSave(enabled)
}

// ========== 网络配置委托方法 ==========

// GetSSHTimeout 获取SSH超时时间 - 委托给网络配置管理器
func (c *Config) GetSSHTimeout() time.Duration {
	return c.networkConfig.GetSSHTimeout()
}

// SetSSHTimeout 设置SSH超时时间 - 委托给网络配置管理器
func (c *Config) SetSSHTimeout(seconds int) {
	c.networkConfig.SetSSHTimeout(seconds)
}

// GetScanTimeout 获取扫描超时时间 - 委托给网络配置管理器
func (c *Config) GetScanTimeout() time.Duration {
	return c.networkConfig.GetScanTimeout()
}

// SetScanTimeout 设置扫描超时时间 - 委托给网络配置管理器
func (c *Config) SetScanTimeout(seconds int) {
	c.networkConfig.SetScanTimeout(seconds)
}

// ========== UI配置委托方法 ==========

// GetTheme 获取主题 - 委托给UI配置管理器
func (c *Config) GetTheme() string {
	return c.uiConfig.GetTheme()
}

// SetTheme 设置主题 - 委托给UI配置管理器
func (c *Config) SetTheme(theme string) {
	c.uiConfig.SetTheme(theme)
}

// GetLanguage 获取语言 - 委托给UI配置管理器
func (c *Config) GetLanguage() string {
	return c.uiConfig.GetLanguage()
}

// SetLanguage 设置语言 - 委托给UI配置管理器
func (c *Config) SetLanguage(language string) {
	c.uiConfig.SetLanguage(language)
}

// GetWindowSize 获取窗口大小 - 委托给UI配置管理器
func (c *Config) GetWindowSize() (int, int) {
	return c.uiConfig.GetWindowSize()
}

// SetWindowSize 设置窗口大小 - 委托给UI配置管理器
func (c *Config) SetWindowSize(width, height int) {
	c.uiConfig.SetWindowSize(width, height)
}

// GetWindowPosition 获取窗口位置 - 委托给UI配置管理器
func (c *Config) GetWindowPosition() (int, int) {
	return c.uiConfig.GetWindowPosition()
}

// SetWindowPosition 设置窗口位置 - 委托给UI配置管理器
func (c *Config) SetWindowPosition(x, y int) {
	c.uiConfig.SetWindowPosition(x, y)
}

// ========== 备份配置委托方法 ==========

// GetBackupEnabled 获取备份启用状态 - 委托给备份配置管理器
func (c *Config) GetBackupEnabled() bool {
	return c.backupConfig.GetBackupEnabled()
}

// SetBackupEnabled 设置备份启用状态 - 委托给备份配置管理器
func (c *Config) SetBackupEnabled(enabled bool) {
	c.backupConfig.SetBackupEnabled(enabled)
}

// GetBackupInterval 获取备份间隔 - 委托给备份配置管理器
func (c *Config) GetBackupInterval() int {
	return c.backupConfig.GetBackupInterval()
}

// SetBackupInterval 设置备份间隔 - 委托给备份配置管理器
func (c *Config) SetBackupInterval(hours int) {
	c.backupConfig.SetBackupInterval(hours)
}

// ========== 自定义设置委托方法 ==========

// GetCustomSetting 获取自定义设置 - 委托给应用配置管理器
func (c *Config) GetCustomSetting(key string) string {
	return c.appConfig.GetCustomSetting(key)
}

// SetCustomSetting 设置自定义设置 - 委托给应用配置管理器
func (c *Config) SetCustomSetting(key, value string) {
	c.appConfig.SetCustomSetting(key, value)
}

// RemoveCustomSetting 移除自定义设置 - 委托给应用配置管理器
func (c *Config) RemoveCustomSetting(key string) {
	c.appConfig.RemoveCustomSetting(key)
}

// ========== 管理方法 ==========

// ResetToDefaults 重置为默认配置 - 委托给应用配置管理器
func (c *Config) ResetToDefaults() {
	c.appConfig.ResetToDefaults()
}

// Validate 验证配置 - 聚合所有配置管理器的验证结果
func (c *Config) Validate() []string {
	var errors []string

	// 验证应用配置
	errors = append(errors, c.appConfig.Validate()...)

	// 验证网络配置
	errors = append(errors, c.networkConfig.Validate()...)

	// 验证UI配置
	errors = append(errors, c.uiConfig.Validate()...)

	// 验证备份配置
	errors = append(errors, c.backupConfig.Validate()...)

	return errors
}

// GetSupportedThemes 获取支持的主题列表 - 委托给UI配置管理器
func (c *Config) GetSupportedThemes() []string {
	return c.uiConfig.GetSupportedThemes()
}

// GetSupportedLanguages 获取支持的语言列表 - 委托给UI配置管理器
func (c *Config) GetSupportedLanguages() []string {
	return c.uiConfig.GetSupportedLanguages()
}

// GetLogLevels 获取支持的日志级别 - 委托给应用配置管理器
func (c *Config) GetLogLevels() []string {
	return c.appConfig.GetSupportedLogLevels()
}

// ========== 配置管理器访问方法 ==========

// GetAppConfigManager 获取应用配置管理器
func (c *Config) GetAppConfigManager() *AppConfigManager {
	return c.appConfig
}

// GetNetworkConfigManager 获取网络配置管理器
func (c *Config) GetNetworkConfigManager() *NetworkConfigManager {
	return c.networkConfig
}

// GetUIConfigManager 获取UI配置管理器
func (c *Config) GetUIConfigManager() *UIConfigManager {
	return c.uiConfig
}

// GetBackupConfigManager 获取备份配置管理器
func (c *Config) GetBackupConfigManager() *BackupConfigManager {
	return c.backupConfig
}
