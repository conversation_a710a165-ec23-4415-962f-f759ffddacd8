package config

import (
	"time"

	"lcheck/data"
)

// NetworkConfigManager 网络配置管理器 - 专门负责网络相关的配置
type NetworkConfigManager struct {
	config *data.AppConfig
}

// NewNetworkConfigManager 创建网络配置管理器
func NewNetworkConfigManager(config *data.AppConfig) *NetworkConfigManager {
	return &NetworkConfigManager{
		config: config,
	}
}

// GetSSHTimeout 获取SSH超时时间（秒）
func (ncm *NetworkConfigManager) GetSSHTimeout() time.Duration {
	return time.Duration(ncm.config.SSHTimeout) * time.Second
}

// SetSSHTimeout 设置SSH超时时间（秒）
func (ncm *NetworkConfigManager) SetSSHTimeout(seconds int) {
	if seconds < 5 {
		seconds = 5
	} else if seconds > 300 {
		seconds = 300
	}
	ncm.config.SSHTimeout = seconds
}

// GetScanTimeout 获取扫描超时时间（秒）
func (ncm *NetworkConfigManager) GetScanTimeout() time.Duration {
	return time.Duration(ncm.config.ScanTimeout) * time.Second
}

// SetScanTimeout 设置扫描超时时间（秒）
func (ncm *NetworkConfigManager) SetScanTimeout(seconds int) {
	if seconds < 60 {
		seconds = 60
	} else if seconds > 3600 {
		seconds = 3600
	}
	ncm.config.ScanTimeout = seconds
}

// GetConnectionRetries 获取连接重试次数
func (ncm *NetworkConfigManager) GetConnectionRetries() int {
	// 从自定义设置中获取，默认为3
	if retries := ncm.config.CustomSettings["connection_retries"]; retries != "" {
		if r := parseInt(retries, 3); r > 0 && r <= 10 {
			return r
		}
	}
	return 3
}

// SetConnectionRetries 设置连接重试次数
func (ncm *NetworkConfigManager) SetConnectionRetries(retries int) {
	if retries < 0 {
		retries = 0
	} else if retries > 10 {
		retries = 10
	}
	
	if ncm.config.CustomSettings == nil {
		ncm.config.CustomSettings = make(map[string]string)
	}
	ncm.config.CustomSettings["connection_retries"] = intToString(retries)
}

// GetKeepAliveInterval 获取保持连接间隔（秒）
func (ncm *NetworkConfigManager) GetKeepAliveInterval() time.Duration {
	// 从自定义设置中获取，默认为30秒
	if interval := ncm.config.CustomSettings["keepalive_interval"]; interval != "" {
		if i := parseInt(interval, 30); i >= 10 && i <= 300 {
			return time.Duration(i) * time.Second
		}
	}
	return 30 * time.Second
}

// SetKeepAliveInterval 设置保持连接间隔（秒）
func (ncm *NetworkConfigManager) SetKeepAliveInterval(seconds int) {
	if seconds < 10 {
		seconds = 10
	} else if seconds > 300 {
		seconds = 300
	}
	
	if ncm.config.CustomSettings == nil {
		ncm.config.CustomSettings = make(map[string]string)
	}
	ncm.config.CustomSettings["keepalive_interval"] = intToString(seconds)
}

// GetMaxIdleConnections 获取最大空闲连接数
func (ncm *NetworkConfigManager) GetMaxIdleConnections() int {
	// 从自定义设置中获取，默认为10
	if maxIdle := ncm.config.CustomSettings["max_idle_connections"]; maxIdle != "" {
		if m := parseInt(maxIdle, 10); m > 0 && m <= 100 {
			return m
		}
	}
	return 10
}

// SetMaxIdleConnections 设置最大空闲连接数
func (ncm *NetworkConfigManager) SetMaxIdleConnections(maxIdle int) {
	if maxIdle < 1 {
		maxIdle = 1
	} else if maxIdle > 100 {
		maxIdle = 100
	}
	
	if ncm.config.CustomSettings == nil {
		ncm.config.CustomSettings = make(map[string]string)
	}
	ncm.config.CustomSettings["max_idle_connections"] = intToString(maxIdle)
}

// GetConnectionPoolSize 获取连接池大小
func (ncm *NetworkConfigManager) GetConnectionPoolSize() int {
	// 从自定义设置中获取，默认为最大并发数
	if poolSize := ncm.config.CustomSettings["connection_pool_size"]; poolSize != "" {
		if p := parseInt(poolSize, ncm.config.MaxConcurrency); p > 0 && p <= 200 {
			return p
		}
	}
	return ncm.config.MaxConcurrency
}

// SetConnectionPoolSize 设置连接池大小
func (ncm *NetworkConfigManager) SetConnectionPoolSize(poolSize int) {
	if poolSize < 1 {
		poolSize = 1
	} else if poolSize > 200 {
		poolSize = 200
	}
	
	if ncm.config.CustomSettings == nil {
		ncm.config.CustomSettings = make(map[string]string)
	}
	ncm.config.CustomSettings["connection_pool_size"] = intToString(poolSize)
}

// GetProxyEnabled 获取代理启用状态
func (ncm *NetworkConfigManager) GetProxyEnabled() bool {
	return ncm.config.CustomSettings["proxy_enabled"] == "true"
}

// SetProxyEnabled 设置代理启用状态
func (ncm *NetworkConfigManager) SetProxyEnabled(enabled bool) {
	if ncm.config.CustomSettings == nil {
		ncm.config.CustomSettings = make(map[string]string)
	}
	ncm.config.CustomSettings["proxy_enabled"] = boolToString(enabled)
}

// GetProxyAddress 获取代理地址
func (ncm *NetworkConfigManager) GetProxyAddress() string {
	return ncm.config.CustomSettings["proxy_address"]
}

// SetProxyAddress 设置代理地址
func (ncm *NetworkConfigManager) SetProxyAddress(address string) {
	if ncm.config.CustomSettings == nil {
		ncm.config.CustomSettings = make(map[string]string)
	}
	ncm.config.CustomSettings["proxy_address"] = address
}

// GetProxyPort 获取代理端口
func (ncm *NetworkConfigManager) GetProxyPort() int {
	if port := ncm.config.CustomSettings["proxy_port"]; port != "" {
		if p := parseInt(port, 8080); p > 0 && p <= 65535 {
			return p
		}
	}
	return 8080
}

// SetProxyPort 设置代理端口
func (ncm *NetworkConfigManager) SetProxyPort(port int) {
	if port < 1 {
		port = 1
	} else if port > 65535 {
		port = 65535
	}
	
	if ncm.config.CustomSettings == nil {
		ncm.config.CustomSettings = make(map[string]string)
	}
	ncm.config.CustomSettings["proxy_port"] = intToString(port)
}

// Validate 验证网络配置
func (ncm *NetworkConfigManager) Validate() []string {
	var errors []string
	
	// 验证SSH超时时间
	if ncm.config.SSHTimeout < 5 || ncm.config.SSHTimeout > 300 {
		errors = append(errors, "SSH超时时间必须在5-300秒之间")
	}
	
	// 验证扫描超时时间
	if ncm.config.ScanTimeout < 60 || ncm.config.ScanTimeout > 3600 {
		errors = append(errors, "扫描超时时间必须在60-3600秒之间")
	}
	
	// 验证连接重试次数
	retries := ncm.GetConnectionRetries()
	if retries < 0 || retries > 10 {
		errors = append(errors, "连接重试次数必须在0-10之间")
	}
	
	// 验证代理配置
	if ncm.GetProxyEnabled() {
		if ncm.GetProxyAddress() == "" {
			errors = append(errors, "启用代理时必须设置代理地址")
		}
		if port := ncm.GetProxyPort(); port < 1 || port > 65535 {
			errors = append(errors, "代理端口必须在1-65535之间")
		}
	}
	
	return errors
}

// GetNetworkSettings 获取网络设置摘要
func (ncm *NetworkConfigManager) GetNetworkSettings() map[string]interface{} {
	return map[string]interface{}{
		"sshTimeout":          ncm.GetSSHTimeout().Seconds(),
		"scanTimeout":         ncm.GetScanTimeout().Seconds(),
		"connectionRetries":   ncm.GetConnectionRetries(),
		"keepAliveInterval":   ncm.GetKeepAliveInterval().Seconds(),
		"maxIdleConnections":  ncm.GetMaxIdleConnections(),
		"connectionPoolSize":  ncm.GetConnectionPoolSize(),
		"proxyEnabled":        ncm.GetProxyEnabled(),
		"proxyAddress":        ncm.GetProxyAddress(),
		"proxyPort":           ncm.GetProxyPort(),
	}
}

// 辅助函数
func parseInt(s string, defaultValue int) int {
	// 简单的字符串转整数实现
	if s == "" {
		return defaultValue
	}
	
	result := 0
	for _, char := range s {
		if char >= '0' && char <= '9' {
			result = result*10 + int(char-'0')
		} else {
			return defaultValue
		}
	}
	return result
}

func intToString(i int) string {
	if i == 0 {
		return "0"
	}
	
	var result []byte
	for i > 0 {
		result = append([]byte{byte(i%10+'0')}, result...)
		i /= 10
	}
	return string(result)
}

func boolToString(b bool) string {
	if b {
		return "true"
	}
	return "false"
}
