package config

import (
	"lcheck/data"
)

// AppConfigManager 应用配置管理器 - 专门负责应用级别的配置
type AppConfigManager struct {
	config *data.AppConfig
}

// NewAppConfigManager 创建应用配置管理器
func NewAppConfigManager() *AppConfigManager {
	return &AppConfigManager{
		config: getDefaultAppConfig(),
	}
}

// getDefaultAppConfig 获取默认应用配置
func getDefaultAppConfig() *data.AppConfig {
	return &data.AppConfig{
		Version:         "3.2.0",
		DataDir:         "./data",
		LogLevel:        "info",
		MaxConcurrency:  10,
		SSHTimeout:      30,
		ScanTimeout:     300,
		AutoSave:        true,
		BackupEnabled:   false,
		BackupInterval:  24,
		Theme:           "light",
		Language:        "zh-CN",
		WindowWidth:     1200,
		WindowHeight:    800,
		WindowX:         100,
		WindowY:         100,
		CustomSettings:  make(map[string]string),
	}
}

// GetConfig 获取完整配置
func (acm *AppConfigManager) GetConfig() *data.AppConfig {
	return acm.config
}

// GetVersion 获取版本
func (acm *AppConfigManager) GetVersion() string {
	return acm.config.Version
}

// GetDataDir 获取数据目录
func (acm *AppConfigManager) GetDataDir() string {
	return acm.config.DataDir
}

// SetDataDir 设置数据目录
func (acm *AppConfigManager) SetDataDir(dir string) {
	if dir != "" {
		acm.config.DataDir = dir
	}
}

// GetLogLevel 获取日志级别
func (acm *AppConfigManager) GetLogLevel() string {
	return acm.config.LogLevel
}

// SetLogLevel 设置日志级别
func (acm *AppConfigManager) SetLogLevel(level string) {
	validLevels := []string{"debug", "info", "warn", "error"}
	for _, validLevel := range validLevels {
		if level == validLevel {
			acm.config.LogLevel = level
			return
		}
	}
}

// GetMaxConcurrency 获取最大并发数
func (acm *AppConfigManager) GetMaxConcurrency() int {
	return acm.config.MaxConcurrency
}

// SetMaxConcurrency 设置最大并发数
func (acm *AppConfigManager) SetMaxConcurrency(concurrency int) {
	if concurrency < 1 {
		concurrency = 1
	} else if concurrency > 100 {
		concurrency = 100
	}
	acm.config.MaxConcurrency = concurrency
}

// GetAutoSave 获取自动保存设置
func (acm *AppConfigManager) GetAutoSave() bool {
	return acm.config.AutoSave
}

// SetAutoSave 设置自动保存
func (acm *AppConfigManager) SetAutoSave(enabled bool) {
	acm.config.AutoSave = enabled
}

// GetCustomSetting 获取自定义设置
func (acm *AppConfigManager) GetCustomSetting(key string) string {
	if acm.config.CustomSettings == nil {
		return ""
	}
	return acm.config.CustomSettings[key]
}

// SetCustomSetting 设置自定义设置
func (acm *AppConfigManager) SetCustomSetting(key, value string) {
	if acm.config.CustomSettings == nil {
		acm.config.CustomSettings = make(map[string]string)
	}
	acm.config.CustomSettings[key] = value
}

// RemoveCustomSetting 移除自定义设置
func (acm *AppConfigManager) RemoveCustomSetting(key string) {
	if acm.config.CustomSettings != nil {
		delete(acm.config.CustomSettings, key)
	}
}

// ResetToDefaults 重置为默认配置
func (acm *AppConfigManager) ResetToDefaults() {
	// 保留窗口位置和大小
	windowWidth := acm.config.WindowWidth
	windowHeight := acm.config.WindowHeight
	windowX := acm.config.WindowX
	windowY := acm.config.WindowY
	
	// 重置为默认配置
	acm.config = getDefaultAppConfig()
	
	// 恢复窗口设置
	acm.config.WindowWidth = windowWidth
	acm.config.WindowHeight = windowHeight
	acm.config.WindowX = windowX
	acm.config.WindowY = windowY
}

// Validate 验证应用配置
func (acm *AppConfigManager) Validate() []string {
	var errors []string
	
	// 验证日志级别
	validLogLevels := []string{"debug", "info", "warn", "error"}
	validLogLevel := false
	for _, level := range validLogLevels {
		if acm.config.LogLevel == level {
			validLogLevel = true
			break
		}
	}
	if !validLogLevel {
		errors = append(errors, "无效的日志级别")
	}
	
	// 验证并发数
	if acm.config.MaxConcurrency < 1 || acm.config.MaxConcurrency > 100 {
		errors = append(errors, "最大并发数必须在1-100之间")
	}
	
	// 验证数据目录
	if acm.config.DataDir == "" {
		errors = append(errors, "数据目录不能为空")
	}
	
	return errors
}

// GetSupportedLogLevels 获取支持的日志级别
func (acm *AppConfigManager) GetSupportedLogLevels() []string {
	return []string{"debug", "info", "warn", "error"}
}

// Clone 克隆配置
func (acm *AppConfigManager) Clone() *AppConfigManager {
	newConfig := *acm.config
	
	// 深拷贝自定义设置
	if acm.config.CustomSettings != nil {
		newConfig.CustomSettings = make(map[string]string)
		for k, v := range acm.config.CustomSettings {
			newConfig.CustomSettings[k] = v
		}
	}
	
	return &AppConfigManager{
		config: &newConfig,
	}
}

// UpdateFrom 从另一个配置更新
func (acm *AppConfigManager) UpdateFrom(other *AppConfigManager) {
	if other != nil && other.config != nil {
		acm.config = other.config
	}
}
