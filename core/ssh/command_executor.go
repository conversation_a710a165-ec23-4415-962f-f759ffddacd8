package ssh

import (
	"fmt"
	"strings"
	"time"

	"lcheck/data"
)

// CommandExecutor SSH命令执行器 - 专门负责SSH命令的执行
type CommandExecutor struct {
	connectionManager *ConnectionManager
	timeout           time.Duration
}

// NewCommandExecutor 创建SSH命令执行器
func NewCommandExecutor(connectionManager *ConnectionManager, timeout time.Duration) *CommandExecutor {
	return &CommandExecutor{
		connectionManager: connectionManager,
		timeout:           timeout,
	}
}

// ExecuteCommand 执行单个命令
func (ce *CommandExecutor) ExecuteCommand(host data.HostInfo, command string) (string, error) {
	// 创建连接
	client, err := ce.connectionManager.CreateConnection(host)
	if err != nil {
		return "", err
	}
	defer client.Close()

	// 创建会话
	session, err := client.NewSession()
	if err != nil {
		return "", fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	// 执行命令
	output, err := session.Output(command)
	if err != nil {
		return "", fmt.Errorf("执行命令失败: %v", err)
	}

	return strings.TrimSpace(string(output)), nil
}

// ExecuteCommands 执行多个命令
func (ce *CommandExecutor) ExecuteCommands(host data.HostInfo, commands map[string]string) (map[string]string, error) {
	results := make(map[string]string)
	
	// 创建连接
	client, err := ce.connectionManager.CreateConnection(host)
	if err != nil {
		return nil, err
	}
	defer client.Close()

	// 逐个执行命令
	for key, command := range commands {
		session, err := client.NewSession()
		if err != nil {
			results[key] = fmt.Sprintf("创建会话失败: %v", err)
			continue
		}

		output, err := session.Output(command)
		session.Close()

		if err != nil {
			results[key] = fmt.Sprintf("执行失败: %v", err)
		} else {
			results[key] = strings.TrimSpace(string(output))
		}
	}

	return results, nil
}

// ExecuteCommandWithTimeout 执行命令（带超时）
func (ce *CommandExecutor) ExecuteCommandWithTimeout(host data.HostInfo, command string, timeout time.Duration) (string, error) {
	// 创建连接
	client, err := ce.connectionManager.CreateConnection(host)
	if err != nil {
		return "", err
	}
	defer client.Close()

	// 创建会话
	session, err := client.NewSession()
	if err != nil {
		return "", fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	// 创建结果通道
	type result struct {
		output string
		err    error
	}
	resultChan := make(chan result, 1)

	// 在goroutine中执行命令
	go func() {
		output, err := session.Output(command)
		resultChan <- result{
			output: strings.TrimSpace(string(output)),
			err:    err,
		}
	}()

	// 等待结果或超时
	select {
	case res := <-resultChan:
		if res.err != nil {
			return "", fmt.Errorf("执行命令失败: %v", res.err)
		}
		return res.output, nil
	case <-time.After(timeout):
		return "", fmt.Errorf("命令执行超时")
	}
}

// ExecuteInteractiveCommand 执行交互式命令
func (ce *CommandExecutor) ExecuteInteractiveCommand(host data.HostInfo, command string, inputs []string) (string, error) {
	// 创建连接
	client, err := ce.connectionManager.CreateConnection(host)
	if err != nil {
		return "", err
	}
	defer client.Close()

	// 创建会话
	session, err := client.NewSession()
	if err != nil {
		return "", fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	// 设置输入输出
	stdin, err := session.StdinPipe()
	if err != nil {
		return "", fmt.Errorf("获取stdin失败: %v", err)
	}

	// 启动命令
	err = session.Start(command)
	if err != nil {
		return "", fmt.Errorf("启动命令失败: %v", err)
	}

	// 发送输入
	for _, input := range inputs {
		_, err = stdin.Write([]byte(input + "\n"))
		if err != nil {
			return "", fmt.Errorf("发送输入失败: %v", err)
		}
	}
	stdin.Close()

	// 等待命令完成
	output, err := session.Output(command)
	if err != nil {
		return "", fmt.Errorf("执行交互式命令失败: %v", err)
	}

	return strings.TrimSpace(string(output)), nil
}

// ExecuteScript 执行脚本
func (ce *CommandExecutor) ExecuteScript(host data.HostInfo, script string) (string, error) {
	// 将脚本内容写入临时文件并执行
	tempFile := "/tmp/lcheck_script_" + fmt.Sprintf("%d", time.Now().Unix())
	
	// 创建脚本文件
	createCmd := fmt.Sprintf("cat > %s << 'EOF'\n%s\nEOF", tempFile, script)
	_, err := ce.ExecuteCommand(host, createCmd)
	if err != nil {
		return "", fmt.Errorf("创建脚本文件失败: %v", err)
	}

	// 设置执行权限
	chmodCmd := fmt.Sprintf("chmod +x %s", tempFile)
	_, err = ce.ExecuteCommand(host, chmodCmd)
	if err != nil {
		return "", fmt.Errorf("设置脚本权限失败: %v", err)
	}

	// 执行脚本
	output, err := ce.ExecuteCommand(host, tempFile)
	
	// 清理临时文件
	cleanupCmd := fmt.Sprintf("rm -f %s", tempFile)
	ce.ExecuteCommand(host, cleanupCmd)

	if err != nil {
		return "", fmt.Errorf("执行脚本失败: %v", err)
	}

	return output, nil
}

// ExecuteCommandAsRoot 以root权限执行命令
func (ce *CommandExecutor) ExecuteCommandAsRoot(host data.HostInfo, command, sudoPassword string) (string, error) {
	if sudoPassword == "" {
		// 尝试无密码sudo
		sudoCommand := fmt.Sprintf("sudo %s", command)
		return ce.ExecuteCommand(host, sudoCommand)
	}

	// 使用密码sudo
	sudoCommand := fmt.Sprintf("echo '%s' | sudo -S %s", sudoPassword, command)
	return ce.ExecuteCommand(host, sudoCommand)
}

// ValidateCommand 验证命令
func (ce *CommandExecutor) ValidateCommand(command string) error {
	if strings.TrimSpace(command) == "" {
		return fmt.Errorf("命令不能为空")
	}

	// 检查危险命令
	dangerousCommands := []string{
		"rm -rf /",
		"mkfs",
		"dd if=/dev/zero",
		":(){ :|:& };:",
		"chmod -R 777 /",
	}

	lowerCommand := strings.ToLower(command)
	for _, dangerous := range dangerousCommands {
		if strings.Contains(lowerCommand, strings.ToLower(dangerous)) {
			return fmt.Errorf("检测到危险命令，拒绝执行: %s", dangerous)
		}
	}

	return nil
}

// GetCommandHistory 获取命令历史（简化版）
func (ce *CommandExecutor) GetCommandHistory(host data.HostInfo) ([]string, error) {
	output, err := ce.ExecuteCommand(host, "history | tail -20")
	if err != nil {
		return nil, err
	}

	lines := strings.Split(output, "\n")
	var history []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			history = append(history, line)
		}
	}

	return history, nil
}

// GetRunningProcesses 获取运行中的进程
func (ce *CommandExecutor) GetRunningProcesses(host data.HostInfo) (string, error) {
	return ce.ExecuteCommand(host, "ps aux")
}

// GetSystemLoad 获取系统负载
func (ce *CommandExecutor) GetSystemLoad(host data.HostInfo) (string, error) {
	return ce.ExecuteCommand(host, "uptime")
}

// GetDiskUsage 获取磁盘使用情况
func (ce *CommandExecutor) GetDiskUsage(host data.HostInfo) (string, error) {
	return ce.ExecuteCommand(host, "df -h")
}

// GetMemoryUsage 获取内存使用情况
func (ce *CommandExecutor) GetMemoryUsage(host data.HostInfo) (string, error) {
	return ce.ExecuteCommand(host, "free -h")
}

// GetNetworkConnections 获取网络连接
func (ce *CommandExecutor) GetNetworkConnections(host data.HostInfo) (string, error) {
	return ce.ExecuteCommand(host, "netstat -tuln")
}

// SetTimeout 设置超时时间
func (ce *CommandExecutor) SetTimeout(timeout time.Duration) {
	ce.timeout = timeout
}

// GetTimeout 获取超时时间
func (ce *CommandExecutor) GetTimeout() time.Duration {
	return ce.timeout
}
