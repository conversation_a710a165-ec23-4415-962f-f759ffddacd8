package ssh

import (
	"fmt"
	"strings"

	"lcheck/data"
)

// FileOperations SSH文件操作器 - 专门负责SSH文件操作
type FileOperations struct {
	commandExecutor *CommandExecutor
}

// NewFileOperations 创建SSH文件操作器
func NewFileOperations(commandExecutor *CommandExecutor) *FileOperations {
	return &FileOperations{
		commandExecutor: commandExecutor,
	}
}

// FileExists 检查文件是否存在
func (fo *FileOperations) FileExists(host data.HostInfo, filePath string) (bool, error) {
	command := fmt.Sprintf("test -f %s && echo 'exists' || echo 'not exists'", filePath)
	output, err := fo.commandExecutor.ExecuteCommand(host, command)
	if err != nil {
		return false, err
	}
	
	return strings.TrimSpace(output) == "exists", nil
}

// DirectoryExists 检查目录是否存在
func (fo *FileOperations) DirectoryExists(host data.HostInfo, dirPath string) (bool, error) {
	command := fmt.Sprintf("test -d %s && echo 'exists' || echo 'not exists'", dirPath)
	output, err := fo.commandExecutor.ExecuteCommand(host, command)
	if err != nil {
		return false, err
	}
	
	return strings.TrimSpace(output) == "exists", nil
}

// GetFileContent 获取文件内容
func (fo *FileOperations) GetFileContent(host data.HostInfo, filePath string) (string, error) {
	command := fmt.Sprintf("cat %s", filePath)
	return fo.commandExecutor.ExecuteCommand(host, command)
}

// GetFilePermissions 获取文件权限
func (fo *FileOperations) GetFilePermissions(host data.HostInfo, filePath string) (string, error) {
	command := fmt.Sprintf("stat -c '%%a' %s", filePath)
	return fo.commandExecutor.ExecuteCommand(host, command)
}

// GetFileOwner 获取文件所有者
func (fo *FileOperations) GetFileOwner(host data.HostInfo, filePath string) (string, error) {
	command := fmt.Sprintf("stat -c '%%U:%%G' %s", filePath)
	return fo.commandExecutor.ExecuteCommand(host, command)
}

// GetFileSize 获取文件大小
func (fo *FileOperations) GetFileSize(host data.HostInfo, filePath string) (string, error) {
	command := fmt.Sprintf("stat -c '%%s' %s", filePath)
	return fo.commandExecutor.ExecuteCommand(host, command)
}

// GetFileModTime 获取文件修改时间
func (fo *FileOperations) GetFileModTime(host data.HostInfo, filePath string) (string, error) {
	command := fmt.Sprintf("stat -c '%%y' %s", filePath)
	return fo.commandExecutor.ExecuteCommand(host, command)
}

// CreateFile 创建文件
func (fo *FileOperations) CreateFile(host data.HostInfo, filePath, content string) error {
	command := fmt.Sprintf("cat > %s << 'EOF'\n%s\nEOF", filePath, content)
	_, err := fo.commandExecutor.ExecuteCommand(host, command)
	return err
}

// AppendToFile 追加内容到文件
func (fo *FileOperations) AppendToFile(host data.HostInfo, filePath, content string) error {
	command := fmt.Sprintf("echo '%s' >> %s", content, filePath)
	_, err := fo.commandExecutor.ExecuteCommand(host, command)
	return err
}

// DeleteFile 删除文件
func (fo *FileOperations) DeleteFile(host data.HostInfo, filePath string) error {
	command := fmt.Sprintf("rm -f %s", filePath)
	_, err := fo.commandExecutor.ExecuteCommand(host, command)
	return err
}

// CopyFile 复制文件
func (fo *FileOperations) CopyFile(host data.HostInfo, srcPath, dstPath string) error {
	command := fmt.Sprintf("cp %s %s", srcPath, dstPath)
	_, err := fo.commandExecutor.ExecuteCommand(host, command)
	return err
}

// MoveFile 移动文件
func (fo *FileOperations) MoveFile(host data.HostInfo, srcPath, dstPath string) error {
	command := fmt.Sprintf("mv %s %s", srcPath, dstPath)
	_, err := fo.commandExecutor.ExecuteCommand(host, command)
	return err
}

// SetFilePermissions 设置文件权限
func (fo *FileOperations) SetFilePermissions(host data.HostInfo, filePath, permissions string) error {
	command := fmt.Sprintf("chmod %s %s", permissions, filePath)
	_, err := fo.commandExecutor.ExecuteCommand(host, command)
	return err
}

// SetFileOwner 设置文件所有者
func (fo *FileOperations) SetFileOwner(host data.HostInfo, filePath, owner string) error {
	command := fmt.Sprintf("chown %s %s", owner, filePath)
	_, err := fo.commandExecutor.ExecuteCommand(host, command)
	return err
}

// CreateDirectory 创建目录
func (fo *FileOperations) CreateDirectory(host data.HostInfo, dirPath string) error {
	command := fmt.Sprintf("mkdir -p %s", dirPath)
	_, err := fo.commandExecutor.ExecuteCommand(host, command)
	return err
}

// DeleteDirectory 删除目录
func (fo *FileOperations) DeleteDirectory(host data.HostInfo, dirPath string) error {
	command := fmt.Sprintf("rm -rf %s", dirPath)
	_, err := fo.commandExecutor.ExecuteCommand(host, command)
	return err
}

// ListDirectory 列出目录内容
func (fo *FileOperations) ListDirectory(host data.HostInfo, dirPath string) ([]string, error) {
	command := fmt.Sprintf("ls -la %s", dirPath)
	output, err := fo.commandExecutor.ExecuteCommand(host, command)
	if err != nil {
		return nil, err
	}

	lines := strings.Split(output, "\n")
	var files []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" && !strings.HasPrefix(line, "total") {
			files = append(files, line)
		}
	}

	return files, nil
}

// FindFiles 查找文件
func (fo *FileOperations) FindFiles(host data.HostInfo, searchPath, pattern string) ([]string, error) {
	command := fmt.Sprintf("find %s -name '%s' -type f", searchPath, pattern)
	output, err := fo.commandExecutor.ExecuteCommand(host, command)
	if err != nil {
		return nil, err
	}

	lines := strings.Split(output, "\n")
	var files []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			files = append(files, line)
		}
	}

	return files, nil
}

// SearchInFile 在文件中搜索内容
func (fo *FileOperations) SearchInFile(host data.HostInfo, filePath, pattern string) ([]string, error) {
	command := fmt.Sprintf("grep -n '%s' %s", pattern, filePath)
	output, err := fo.commandExecutor.ExecuteCommand(host, command)
	if err != nil {
		// grep没有找到匹配项时会返回错误，这是正常的
		return []string{}, nil
	}

	lines := strings.Split(output, "\n")
	var matches []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			matches = append(matches, line)
		}
	}

	return matches, nil
}

// GetFileChecksum 获取文件校验和
func (fo *FileOperations) GetFileChecksum(host data.HostInfo, filePath string) (string, error) {
	command := fmt.Sprintf("md5sum %s", filePath)
	output, err := fo.commandExecutor.ExecuteCommand(host, command)
	if err != nil {
		return "", err
	}

	// md5sum输出格式: "checksum filename"
	parts := strings.Fields(output)
	if len(parts) > 0 {
		return parts[0], nil
	}

	return "", fmt.Errorf("无法解析校验和")
}

// CompressFile 压缩文件
func (fo *FileOperations) CompressFile(host data.HostInfo, filePath, archivePath string) error {
	command := fmt.Sprintf("tar -czf %s %s", archivePath, filePath)
	_, err := fo.commandExecutor.ExecuteCommand(host, command)
	return err
}

// ExtractFile 解压文件
func (fo *FileOperations) ExtractFile(host data.HostInfo, archivePath, extractPath string) error {
	command := fmt.Sprintf("tar -xzf %s -C %s", archivePath, extractPath)
	_, err := fo.commandExecutor.ExecuteCommand(host, command)
	return err
}

// GetDiskUsage 获取目录磁盘使用情况
func (fo *FileOperations) GetDiskUsage(host data.HostInfo, dirPath string) (string, error) {
	command := fmt.Sprintf("du -sh %s", dirPath)
	return fo.commandExecutor.ExecuteCommand(host, command)
}

// ValidateFilePath 验证文件路径
func (fo *FileOperations) ValidateFilePath(filePath string) error {
	if strings.TrimSpace(filePath) == "" {
		return fmt.Errorf("文件路径不能为空")
	}

	// 检查危险路径
	dangerousPaths := []string{
		"/etc/passwd",
		"/etc/shadow",
		"/boot",
		"/dev",
		"/proc",
		"/sys",
	}

	for _, dangerous := range dangerousPaths {
		if strings.HasPrefix(filePath, dangerous) {
			return fmt.Errorf("拒绝访问系统关键路径: %s", dangerous)
		}
	}

	return nil
}

// GetFileInfo 获取文件详细信息
func (fo *FileOperations) GetFileInfo(host data.HostInfo, filePath string) (map[string]string, error) {
	info := make(map[string]string)

	// 检查文件是否存在
	exists, err := fo.FileExists(host, filePath)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, fmt.Errorf("文件不存在: %s", filePath)
	}

	// 获取各种信息
	if permissions, err := fo.GetFilePermissions(host, filePath); err == nil {
		info["permissions"] = permissions
	}

	if owner, err := fo.GetFileOwner(host, filePath); err == nil {
		info["owner"] = owner
	}

	if size, err := fo.GetFileSize(host, filePath); err == nil {
		info["size"] = size
	}

	if modTime, err := fo.GetFileModTime(host, filePath); err == nil {
		info["modTime"] = modTime
	}

	if checksum, err := fo.GetFileChecksum(host, filePath); err == nil {
		info["checksum"] = checksum
	}

	return info, nil
}
