package ssh

import (
	"fmt"
	"net"
	"time"

	"golang.org/x/crypto/ssh"
	"lcheck/data"
)

// ConnectionManager SSH连接管理器 - 专门负责SSH连接的建立和管理
type ConnectionManager struct {
	timeout time.Duration
}

// NewConnectionManager 创建SSH连接管理器
func NewConnectionManager(timeout time.Duration) *ConnectionManager {
	return &ConnectionManager{
		timeout: timeout,
	}
}

// CreateConnection 创建SSH连接
func (cm *ConnectionManager) CreateConnection(host data.HostInfo) (*ssh.Client, error) {
	// 创建SSH配置
	config, err := cm.createSSHConfig(host)
	if err != nil {
		return nil, fmt.Errorf("创建SSH配置失败: %v", err)
	}

	// 建立连接
	addr := fmt.Sprintf("%s:%s", host.Host, host.Port)
	client, err := ssh.Dial("tcp", addr, config)
	if err != nil {
		return nil, fmt.Errorf("SSH连接失败: %v", err)
	}

	return client, nil
}

// TestConnection 测试SSH连接
func (cm *ConnectionManager) TestConnection(host data.HostInfo) error {
	client, err := cm.CreateConnection(host)
	if err != nil {
		return err
	}
	defer client.Close()

	// 执行简单命令测试连接
	session, err := client.NewSession()
	if err != nil {
		return fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	// 执行echo命令测试
	output, err := session.Output("echo 'connection test'")
	if err != nil {
		return fmt.Errorf("执行测试命令失败: %v", err)
	}

	if string(output) == "" {
		return fmt.Errorf("测试命令无输出")
	}

	return nil
}

// createSSHConfig 创建SSH配置
func (cm *ConnectionManager) createSSHConfig(host data.HostInfo) (*ssh.ClientConfig, error) {
	config := &ssh.ClientConfig{
		User:    host.Username,
		Timeout: cm.timeout,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 在生产环境中应该验证主机密钥
	}

	// 根据是否有密码来设置认证方法
	if host.Password != "" {
		// 使用密码认证
		config.Auth = []ssh.AuthMethod{
			ssh.Password(host.Password),
		}
	} else {
		// 没有密码，尝试使用默认的SSH密钥
		return nil, fmt.Errorf("需要提供密码进行SSH认证")
	}

	return config, nil
}

// loadPrivateKey 加载私钥
func (cm *ConnectionManager) loadPrivateKey(keyPath, passphrase string) (ssh.Signer, error) {
	// 这里应该实现私钥加载逻辑
	// 为了简化，暂时返回错误
	return nil, fmt.Errorf("私钥加载功能暂未实现")
}

// ValidateHost 验证主机信息
func (cm *ConnectionManager) ValidateHost(host data.HostInfo) error {
	// 验证主机地址
	if host.Host == "" {
		return fmt.Errorf("主机地址不能为空")
	}

	// 验证端口
	if host.Port == "" {
		return fmt.Errorf("端口不能为空")
	}

	// 验证端口格式
	if _, err := net.LookupPort("tcp", host.Port); err != nil {
		return fmt.Errorf("无效的端口: %s", host.Port)
	}

	// 验证用户名
	if host.Username == "" {
		return fmt.Errorf("用户名不能为空")
	}

	// 验证认证信息
	if host.Password == "" {
		return fmt.Errorf("需要提供密码进行SSH认证")
	}

	return nil
}

// GetConnectionInfo 获取连接信息
func (cm *ConnectionManager) GetConnectionInfo(host data.HostInfo) map[string]interface{} {
	return map[string]interface{}{
		"host":     host.Host,
		"port":     host.Port,
		"username": host.Username,
		"authType": "password", // 固定为密码认证
		"timeout":  cm.timeout.String(),
	}
}

// SetTimeout 设置超时时间
func (cm *ConnectionManager) SetTimeout(timeout time.Duration) {
	cm.timeout = timeout
}

// GetTimeout 获取超时时间
func (cm *ConnectionManager) GetTimeout() time.Duration {
	return cm.timeout
}

// IsHostReachable 检查主机是否可达
func (cm *ConnectionManager) IsHostReachable(host data.HostInfo) bool {
	addr := fmt.Sprintf("%s:%s", host.Host, host.Port)
	conn, err := net.DialTimeout("tcp", addr, cm.timeout)
	if err != nil {
		return false
	}
	defer conn.Close()
	return true
}

// GetSupportedAuthTypes 获取支持的认证类型
func (cm *ConnectionManager) GetSupportedAuthTypes() []string {
	return []string{"password", "key"}
}

// CreateConnectionPool 创建连接池（简化版）
func (cm *ConnectionManager) CreateConnectionPool(hosts []data.HostInfo, poolSize int) map[string]*ssh.Client {
	pool := make(map[string]*ssh.Client)
	
	for i, host := range hosts {
		if i >= poolSize {
			break
		}
		
		client, err := cm.CreateConnection(host)
		if err != nil {
			continue
		}
		
		pool[host.ID] = client
	}
	
	return pool
}

// CloseConnectionPool 关闭连接池
func (cm *ConnectionManager) CloseConnectionPool(pool map[string]*ssh.Client) {
	for _, client := range pool {
		if client != nil {
			client.Close()
		}
	}
}
