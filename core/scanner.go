package core

import (
	"time"

	"lcheck/core/engines"
	"lcheck/data"
)

// Scanner 扫描器 - 模块化门面，委托给专门的扫描引擎
type Scanner struct {
	storage     *data.Storage
	scanManager *engines.ScanManager
}

// NewScanner 创建扫描器 - 使用模块化引擎
func NewScanner(storage *data.Storage, sshTimeout, scanTimeout time.Duration, maxConcurrency int) *Scanner {
	// 创建SSH客户端和基线检查器
	sshClient := NewSSHClient(sshTimeout)
	baselineChecker := NewBaselineChecker(sshClient)

	// 创建扫描引擎（使用接口）
	singleScanner := engines.NewSingleScanner(sshClient, baselineChecker, scanTimeout)
	batchScanner := engines.NewBatchScanner(singleScanner, maxConcurrency)
	scanManager := engines.NewScanManager(singleScanner, batchScanner)

	return &Scanner{
		storage:     storage,
		scanManager: scanManager,
	}
}

// ========== 委托给扫描管理器的方法 ==========

// GetProgressChannel 获取进度通道 - 委托给扫描管理器
func (s *Scanner) GetProgressChannel() <-chan data.ScanProgress {
	return s.scanManager.GetProgressChannel()
}

// ScanSingleHost 扫描单个主机 - 委托给扫描管理器
func (s *Scanner) ScanSingleHost(host data.HostInfo) (string, error) {
	return s.scanManager.StartSingleScan(host)
}

// ScanMultipleHosts 批量扫描主机 - 委托给扫描管理器
func (s *Scanner) ScanMultipleHosts(hosts []data.HostInfo, concurrency int) (string, error) {
	return s.scanManager.StartBatchScan(hosts, concurrency)
}

// CancelScan 取消扫描 - 委托给扫描管理器
func (s *Scanner) CancelScan(scanID string) error {
	return s.scanManager.CancelScan(scanID)
}

// GetActiveScans 获取活动扫描 - 委托给扫描管理器
func (s *Scanner) GetActiveScans() map[string]interface{} {
	scans := s.scanManager.GetActiveScans()
	result := make(map[string]interface{})

	for id, ctx := range scans {
		result[id] = map[string]interface{}{
			"scanID":    ctx.ScanID,
			"status":    ctx.Status,
			"startTime": ctx.StartTime,
			"progress":  ctx.Progress,
		}
	}

	return result
}

// GetScanStatus 获取扫描状态 - 委托给扫描管理器
func (s *Scanner) GetScanStatus(scanID string) (map[string]interface{}, error) {
	ctx, err := s.scanManager.GetScanStatus(scanID)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"scanID":    ctx.ScanID,
		"status":    ctx.Status,
		"startTime": ctx.StartTime,
		"progress":  ctx.Progress,
	}, nil
}

// ========== 存储相关方法 ==========

// SaveScanResult 保存扫描结果
func (s *Scanner) SaveScanResult(result data.ScanResult) error {
	return s.storage.SaveScanResult(result)
}

// SaveBatchScanResult 保存批量扫描结果
func (s *Scanner) SaveBatchScanResult(result data.BatchScanResult) error {
	return s.storage.SaveBatchScanResult(result)
}

// ========== 统计和管理方法 ==========

// GetStatistics 获取扫描统计信息
func (s *Scanner) GetStatistics() map[string]interface{} {
	return s.scanManager.GetStatistics()
}

// GetSSHClient 获取SSH客户端
func (s *Scanner) GetSSHClient() *SSHClient {
	// 创建一个新的SSH客户端实例
	return NewSSHClient(30 * time.Second) // 使用默认超时时间
}

// Close 关闭扫描器
func (s *Scanner) Close() {
	s.scanManager.Close()
}
